# WebRTC iOS 编译指南

本文档记录了在 macOS 上编译 WebRTC iOS 框架的完整过程。

## 环境要求

- macOS 系统
- Xcode 开发工具
- Python 3.x
- Git
- 足够的磁盘空间（约 10GB+）

## 1. 准备工作

### 1.1 安装 depot_tools

```bash
# 克隆 depot_tools
git clone https://chromium.googlesource.com/chromium/tools/depot_tools.git

# 将 depot_tools 添加到 PATH
export PATH="$PWD/depot_tools:$PATH"
```

### 1.2 设置工作目录

```bash
# 创建工作目录
mkdir -p webrtc && cd webrtc

# 配置 gclient
gclient config --name src https://webrtc.googlesource.com/src
```

## 2. 获取源码

### 2.1 如果已有 WebRTC 源码

如果您已经有 WebRTC 源码目录，需要调整目录结构：

```bash
# 将现有源码移动到 src 目录
mv /path/to/webrtc-source src

# 更新远程仓库 URL（如果需要）
cd src
git remote set-url origin https://webrtc.googlesource.com/src
cd ..
```

### 2.2 同步依赖（可选）

```bash
# 同步所有依赖（需要大量时间和网络带宽）
gclient sync
```

**注意：** 如果网络条件不好，可以跳过此步骤，直接进行编译。

## 3. 编译过程

### 3.1 设置环境变量

```bash
export PATH="../depot_tools:$PATH"
cd src
```

### 3.2 生成构建文件

#### 设备端 ARM64 架构

```bash
gn gen out_ios_libs/device_arm64_libs --args='target_os="ios" ios_enable_code_signing=false is_component_build=false rtc_include_tests=false is_debug=false target_environment="device" target_cpu="arm64" ios_deployment_target="14.0" rtc_libvpx_build_vp9=false use_lld=true rtc_enable_objc_symbol_export=true use_siso=false enable_dsyms=true enable_stripping=true'
```

#### 模拟器 ARM64 架构

```bash
gn gen out_ios_libs/simulator_arm64_libs --args='target_os="ios" ios_enable_code_signing=false is_component_build=false rtc_include_tests=false is_debug=false target_environment="simulator" target_cpu="arm64" ios_deployment_target="14.0" rtc_libvpx_build_vp9=false use_lld=true rtc_enable_objc_symbol_export=true use_siso=false enable_dsyms=true enable_stripping=true'
```

#### 模拟器 x64 架构

```bash
gn gen out_ios_libs/simulator_x64_libs --args='target_os="ios" ios_enable_code_signing=false is_component_build=false rtc_include_tests=false is_debug=false target_environment="simulator" target_cpu="x64" ios_deployment_target="14.0" rtc_libvpx_build_vp9=false use_lld=true rtc_enable_objc_symbol_export=true use_siso=false enable_dsyms=true enable_stripping=true'
```

### 3.3 执行编译

```bash
# 编译设备端 ARM64
ninja -C out_ios_libs/device_arm64_libs framework_objc

# 编译模拟器 ARM64
ninja -C out_ios_libs/simulator_arm64_libs framework_objc

# 编译模拟器 x64
ninja -C out_ios_libs/simulator_x64_libs framework_objc
```

## 4. 创建通用框架

### 4.1 创建模拟器通用框架

```bash
# 创建目录
mkdir -p out_ios_libs/simulator_universal_libs

# 复制框架结构
cp -R out_ios_libs/simulator_arm64_libs/WebRTC.framework out_ios_libs/simulator_universal_libs/

# 合并二进制文件
lipo -create \
  out_ios_libs/simulator_arm64_libs/WebRTC.framework/WebRTC \
  out_ios_libs/simulator_x64_libs/WebRTC.framework/WebRTC \
  -output out_ios_libs/simulator_universal_libs/WebRTC.framework/WebRTC
```

### 4.2 创建 XCFramework

```bash
xcodebuild -create-xcframework \
  -framework out_ios_libs/device_arm64_libs/WebRTC.framework \
  -framework out_ios_libs/simulator_universal_libs/WebRTC.framework \
  -output out_ios_libs/WebRTC.xcframework
```

## 5. 验证结果

```bash
# 检查生成的框架
ls -la out_ios_libs/

# 查看 XCFramework 信息
xcodebuild -showBuildSettings -xcframework out_ios_libs/WebRTC.xcframework
```

## 6. 常见问题

### 6.1 siso 认证错误

如果遇到 `need to run siso login` 错误，确保在 GN 参数中添加 `use_siso=false`。

### 6.2 网络问题

如果 `gclient sync` 失败，可以：
- 使用代理
- 跳过同步步骤，直接编译
- 分批下载依赖

### 6.3 磁盘空间不足

编译过程需要大量磁盘空间，确保至少有 10GB 可用空间。

## 7. 编译参数说明

| 参数 | 说明 |
|------|------|
| `target_os="ios"` | 目标操作系统为 iOS |
| `ios_enable_code_signing=false` | 禁用代码签名 |
| `is_component_build=false` | 静态链接构建 |
| `rtc_include_tests=false` | 不包含测试代码 |
| `is_debug=false` | 发布版本构建 |
| `target_environment` | 目标环境（device/simulator） |
| `target_cpu` | 目标 CPU 架构 |
| `ios_deployment_target` | iOS 最低支持版本 |
| `use_siso=false` | 禁用 siso 构建系统 |

## 8. 输出文件

编译完成后，主要输出文件位于：
- `out_ios_libs/WebRTC.xcframework` - 通用 XCFramework
- `out_ios_libs/device_arm64_libs/WebRTC.framework` - 设备端框架
- `out_ios_libs/simulator_universal_libs/WebRTC.framework` - 模拟器通用框架

## 9. 使用建议

1. 首次编译建议使用发布版本（`is_debug=false`）
2. 如果只需要特定架构，可以只编译对应的目标
3. 编译过程耗时较长，建议在性能较好的机器上进行
4. 定期清理构建缓存以节省磁盘空间

---

**编译时间参考：**
- 单个架构：约 30-60 分钟
- 全部架构：约 2-3 小时

**注意：** 编译时间取决于硬件配置和网络状况。
