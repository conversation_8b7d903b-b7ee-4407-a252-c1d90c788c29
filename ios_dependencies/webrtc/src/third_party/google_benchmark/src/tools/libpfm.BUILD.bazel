# Build rule for libpfm, which is required to collect performance counters for
# BENCHMARK_ENABLE_LIBPFM builds.

load("@rules_foreign_cc//foreign_cc:defs.bzl", "make")

filegroup(
    name = "pfm_srcs",
    srcs = glob(["**"]),
)

make(
    name = "libpfm",
    lib_source = ":pfm_srcs",
    lib_name = "libpfm",
    copts = [
        "-Wno-format-truncation",
        "-Wno-use-after-free",
    ],
    visibility = [
        "//visibility:public",
    ],
)
