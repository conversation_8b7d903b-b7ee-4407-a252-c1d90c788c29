name: test-bindings

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  python_bindings:
    name: Test GBM Python bindings on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: 3.11
      - name: Install GBM Python bindings on ${{ matrix.os }}
        run: python -m pip install .
      - name: Run bindings example on ${{ matrix.os }}
        run:
          python bindings/python/google_benchmark/example.py
