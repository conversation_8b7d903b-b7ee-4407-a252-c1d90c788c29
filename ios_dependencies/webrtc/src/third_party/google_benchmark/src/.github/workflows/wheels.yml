name: Build and upload Python wheels

on:
  workflow_dispatch:
  release:
    types:
      - published

jobs:
  build_sdist:
    name: Build source distribution
    runs-on: ubuntu-latest
    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
      - run: python -m pip install build
      - name: Build sdist
        run: python -m build --sdist
      - uses: actions/upload-artifact@v4
        with:
          name: dist-sdist
          path: dist/*.tar.gz

  build_wheels:
    name: Build Google Benchmark wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-13, macos-14, windows-latest]

    steps:
      - name: Check out Google Benchmark
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v5
        name: Install Python 3.12
        with:
          python-version: "3.12"
      - run: pip install --upgrade pip uv

      - name: Set up QEMU
        if: runner.os == 'Linux'
        uses: docker/setup-qemu-action@v3
        with:
          platforms: all

      - name: Build wheels on ${{ matrix.os }} using cibuildwheel
        uses: pypa/cibuildwheel@v2.20
        env:
          CIBW_BUILD: "cp310-* cp311-* cp312-*"
          CIBW_BUILD_FRONTEND: "build[uv]"
          CIBW_SKIP: "*-musllinux_*"
          CIBW_TEST_SKIP: "cp38-macosx_*:arm64"
          CIBW_ARCHS_LINUX: auto64 aarch64
          CIBW_ARCHS_WINDOWS: auto64
          CIBW_BEFORE_ALL_LINUX: bash .github/install_bazel.sh
          # Grab the rootless Bazel installation inside the container.
          CIBW_ENVIRONMENT_LINUX: PATH=$PATH:$HOME/bin
          CIBW_TEST_COMMAND: python {project}/bindings/python/google_benchmark/example.py
          # unused by Bazel, but needed explicitly by delocate on MacOS.
          MACOSX_DEPLOYMENT_TARGET: "10.14"

      - name: Upload Google Benchmark ${{ matrix.os }} wheels
        uses: actions/upload-artifact@v4
        with:
          name: dist-${{ matrix.os }}
          path: wheelhouse/*.whl

  merge_wheels:
    name: Merge all built wheels into one artifact
    runs-on: ubuntu-latest
    needs: build_wheels
    steps:
      - name: Merge wheels
        uses: actions/upload-artifact/merge@v4
        with:
          name: dist
          pattern: dist-*
          delete-merged: true

  pypi_upload:
    name: Publish google-benchmark wheels to PyPI
    needs: [merge_wheels]
    runs-on: ubuntu-latest
    if: github.event_name == 'release' && github.event.action == 'published'
    permissions:
      id-token: write
    steps:
      - uses: actions/download-artifact@v4
        with:
          path: dist
      - uses: pypa/gh-action-pypi-publish@release/v1
