src/core/ext/upb-gen/** linguist-generated=true
src/core/ext/upbdefs-gen/** linguist-generated=true
Makefile linguist-generated=true
BUILD.gn linguist-generated=true
CMakeLists.txt linguist-generated=true
build_autogenerated.yaml linguist-generated=true
config.m4 linguist-generated=true
config.w32 linguist-generated=true
gRPC-C++.podspec linguist-generated=true
gRPC-Core.podspec linguist-generated=true
gRPC-ProtoRPC.podspec linguist-generated=true
gRPC-RxLibrary.podspec linguist-generated=true
gRPC.podspec linguist-generated=true
grpc.gemspec linguist-generated=true
grpc.def linguist-generated=true
package.xml linguist-generated=true
Package.swift linguist-generated=true
src/python/grpcio/grpc_core_dependencies.py linguist-generated=true
src/ruby/ext/grpc/rb_grpc_imports.generated.h linguist-generated=true
src/ruby/ext/grpc/rb_grpc_imports.generated.c linguist-generated=true
include/grpc/module.modulemap linguist-generated=true
test/core/security/grpc_tls_credentials_options_comparator_test.cc linguist-generated=true
tools/distrib/python/xds_protos/** linguist-generated=true
tools/doxygen/Doxyfile.c++ linguist-generated=true
tools/doxygen/Doxyfile.c++.internal linguist-generated=true
tools/doxygen/Doxyfile.core linguist-generated=true
tools/doxygen/Doxyfile.core.internal linguist-generated=true
tools/run_tests/sources_and_headers.json linguist-generated=true
tools/run_tests/tests.json linguist-generated=true
tools/run_tests/generated/tests.json linguist-generated=true
tools/run_tests/generated/sources_and_headers.json linguist-generated=true
src/core/config/config_vars.h linguist-generated=true
src/core/config/config_vars.cc linguist-generated=true
src/core/credentials/transport/tls/grpc_tls_credentials_options.h linguist-generated=true
src/core/telemetry/stats_data.h linguist-generated=true
src/core/telemetry/stats_data.cc linguist-generated=true
src/core/lib/experiments/experiments.h linguist-generated=true
src/core/lib/experiments/experiments.cc linguist-generated=true
bazel/experiments.bzl linguist-generated=true
test/cpp/microbenchmarks/huffman_geometries/** linguist-generated=true
doc/trace_flags.md linguist-generated=true
src/core/lib/debug/trace_flags.h linguist-generated=true
src/core/lib/debug/trace_flags.cc linguist-generated=true
src/python/grpcio_observability/observability_lib_deps.py linguist-generated=true
