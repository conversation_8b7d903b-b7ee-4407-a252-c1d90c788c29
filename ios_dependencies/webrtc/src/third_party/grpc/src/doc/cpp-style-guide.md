GRPC C++ STYLE GUIDE
=====================

The majority of gRPC's C++ requirements are drawn from the [Google C++ style
guide] (https://google.github.io/styleguide/cppguide.html). Additionally,
as in C, layout rules are defined by clang-format, and all code
should be passed through clang-format. A (docker-based) script to do
so is included in [tools/distrib/clang_format_code.sh](../tools/distrib/clang_format_code.sh).
