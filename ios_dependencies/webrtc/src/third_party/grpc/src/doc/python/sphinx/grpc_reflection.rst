gRPC Reflection
====================

What is gRPC reflection?
---------------------------------------------

Check this out `gRPC Python Server Reflection <https://github.com/grpc/grpc/blob/master/doc/python/server_reflection.md>`_


Example
-------

Refer to the GitHub `reflection example <https://github.com/grpc/grpc/blob/master/examples/python/helloworld/greeter_server_with_reflection.py>`_


Module Contents
---------------

Server-side
...........

.. automodule:: grpc_reflection.v1alpha.reflection

Client-side
...........

.. automodule:: grpc_reflection.v1alpha.proto_reflection_descriptor_database
   :member-order: bysource
