# Copyright 2023 The gRPC Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM python:3.9-slim-bookworm

RUN apt-get update -y && apt-get upgrade -y && apt-get install -y build-essential clang curl

WORKDIR /workdir

RUN ln -s /usr/bin/python3 /usr/bin/python
RUN mkdir /artifacts

COPY . .
RUN OVERRIDE_BAZEL_VERSION=5.4.0 tools/bazel build //examples/cpp/csm:csm_greeter_server
RUN cp -rL /workdir/bazel-bin/examples/cpp/csm/csm_greeter_server /artifacts/

FROM python:3.9-slim-bookworm

RUN apt-get update \
    && apt-get -y upgrade \
    && apt-get -y autoremove \
    && apt-get install -y curl

COPY --from=0 /artifacts ./

ENV GRPC_EXPERIMENTAL_XDS_ENABLE_OVERRIDE_HOST=true

ENTRYPOINT ["/csm_greeter_server"]
