# Copyright 2023 The gRPC Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM python:3.9-slim-bookworm

RUN apt-get update -y && apt-get upgrade -y && apt-get install -y build-essential clang curl

WORKDIR /workdir

RUN ln -s /usr/bin/python3 /usr/bin/python
RUN mkdir /artifacts

COPY . .
RUN tools/bazel build //examples/cpp/csm/observability:csm_greeter_server
RUN cp -rL /workdir/bazel-bin/examples/cpp/csm/observability/csm_greeter_server /artifacts/

FROM python:3.9-slim-bookworm

ENV GRPC_TRACE="xds_client,xds_resolver,xds_cluster_manager_lb,cds_lb,xds_cluster_resolver_lb,priority_lb,xds_cluster_impl_lb,weighted_target_lb,xds_server_config_fetcher,ring_hash_lb,outlier_detection_lb,xds_wrr_locality_lb,xds_override_host_lb"

RUN apt-get update \
    && apt-get -y upgrade \
    && apt-get -y autoremove \
    && apt-get install -y curl

COPY --from=0 /artifacts ./

ENTRYPOINT ["/csm_greeter_server"]
