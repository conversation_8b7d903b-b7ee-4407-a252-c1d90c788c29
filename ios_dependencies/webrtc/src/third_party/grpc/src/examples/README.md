# Examples

This directory contains examples for all the C-based gRPC implementations. Each
language subdirectory contains a Hello World example and more:

* [Android](android)
* [C++](cpp)
* [Node.js](node)
* [Objective-C](objective-c/helloworld)
* [PHP](php)
* [Python](python/helloworld)
* [Ruby](ruby)

For a complete list of supported languages, see [Supported languages][lang].

For comprehensive documentation, including an [Introduction to gRPC][intro] and
tutorials that use this example code, visit [grpc.io](https://grpc.io).

[intro]: https://grpc.io/docs/what-is-grpc/introduction
[lang]: https://grpc.io/docs/languages/
