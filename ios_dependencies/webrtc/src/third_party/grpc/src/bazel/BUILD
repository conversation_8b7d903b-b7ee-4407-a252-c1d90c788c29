# Copyright 2017 gRPC authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Contains build targets used by Starlark files in the bazel/ directory.
"""

licenses(["notice"])

package(default_visibility = ["//:__subpackages__"])

filegroup(
    name = "_single_module_tester",
    srcs = ["_single_module_tester.py"],
)

filegroup(
    name = "_gevent_test_main",
    srcs = ["_gevent_test_main.py"],
)

filegroup(
    name = "_logging_threshold_test_main",
    srcs = ["_logging_threshold_test_main.py"],
)

###############################################################################
# VISIBILITY TARGETS
#

package_group(
    name = "alt_grpc++_base_legacy",
    packages = [],
)

package_group(
    name = "alt_grpc++_base_unsecure_legacy",
    packages = [],
)

package_group(
    name = "alt_grpc_base_legacy",
    packages = [],
)

package_group(
    name = "census",
    packages = [],
)

package_group(
    name = "channelz",
    packages = [],
)

package_group(
    name = "cli",
    packages = [],
)

package_group(
    name = "client_channel",
    packages = [],
)

package_group(
    name = "core_credentials",
    packages = [],
)

package_group(
    name = "debug_location",
    packages = [],
)

package_group(
    name = "endpoint_tests",
    packages = [],
)

package_group(
    name = "exec_ctx",
    packages = [],
)

package_group(
    name = "grpc_opencensus_plugin",
    packages = [],
)

package_group(
    name = "gpr_public_hdrs",
    packages = [],
)

package_group(
    name = "grpc_public_hdrs",
    packages = [],
)

package_group(
    name = "grpc++_public_hdrs",
    packages = [],
)

package_group(
    name = "grpc_resolver_fake",
    packages = [],
)

package_group(
    name = "grpclb",
    packages = [],
)

package_group(
    name = "httpcli",
    packages = [],
)

package_group(
    name = "json_reader_legacy",
    packages = [],
)

package_group(
    name = "ref_counted_ptr",
    packages = [],
)

package_group(
    name = "tcp_tracer",
    packages = [],
)

package_group(
    name = "trace",
    packages = [],
)

package_group(
    name = "tsi",
    packages = [],
)

package_group(
    name = "tsi_interface",
    packages = [],
)

package_group(
    name = "xds",
    packages = [],
)

package_group(
    name = "xds_client_core",
    packages = [],
)

package_group(
    name = "xds_client_grpc",
    packages = [],
)

package_group(
    name = "grpc_python_observability",
    packages = [],
)

package_group(
    name = "otel_plugin",
    packages = [],
)

package_group(
    name = "grpc_experiments",
    packages = [],
)

package_group(
    name = "event_engine_base_hdrs",
    packages = [],
)

package_group(
    name = "useful",
    packages = [],
)

package_group(
    name = "chaotic_good",
    packages = [],
)

package_group(
    name = "xds_end2end_test_utils",
    packages = [],
)

package_group(
    name = "latent_see",
    packages = [],
)

package_group(
    name = "core_end2end_tests",
    packages = [
    ],
)
