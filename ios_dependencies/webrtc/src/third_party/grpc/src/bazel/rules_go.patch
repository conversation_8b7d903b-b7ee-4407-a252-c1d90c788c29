# This patch works around a problem with Windows RBE described in
# https://github.com/bazelbuild/bazel/issues/11636.  It can be removed
# once that issue is resolved.
diff --git a/go/private/rules/binary.bzl b/go/private/rules/binary.bzl
index 40a17f4d..2741ad71 100644
--- a/go/private/rules/binary.bzl
+++ b/go/private/rules/binary.bzl
@@ -462,8 +462,9 @@ exit /b %GO_EXIT_CODE%
             content = cmd,
         )
         ctx.actions.run(
-            executable = bat,
-            inputs = sdk.headers + sdk.tools + sdk.srcs + ctx.files.srcs + [sdk.go],
+            executable = "cmd.exe",
+            arguments = ["/S", "/C", bat.path.replace("/", "\\")],
+            inputs = sdk.headers + sdk.tools + sdk.srcs + ctx.files.srcs + [sdk.go, bat],
             outputs = [out, gotmp],
             mnemonic = "GoToolchainBinaryBuild",
         )
