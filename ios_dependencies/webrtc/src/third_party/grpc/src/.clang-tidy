---
# Note on checks are disabled on purpose
#
# - abseil-cleanup-ctad
#   Requires C++17 and higher.
#
# - abseil-no-namespace
#   https://bugs.llvm.org/show_bug.cgi?id=47947
#
# - bugprone-exception-escape
#   https://github.com/llvm/llvm-project/issues/54668 (seems to be fixed in LLVM17)
#
# - bugprone-reserved-identifier
#   Some macros need to be defined for portability purpose; e.g. _BSD_SOURCE.
#
# - modernize-redundant-void-arg
#   Some source should be strictly C99 and func(void) should be used.
#
# - google-readability-casting
#   https://github.com/llvm/llvm-project/issues/57959
#
# Note on checks which will be enabled in future. These are good to have but
# it's not activated yet due to the existing issues with the checks.
# Once those issues are clear, these checks can be enabled later.
#
# - bugprone-assignment-in-if-condition
# - bugprone-branch-clone
# - bugprone-easily-swappable-parameters
# - bugprone-implicit-widening-of-multiplication-result
# - bugprone-infinite-loop
# - bugprone-narrowing-conversions
# - bugprone-not-null-terminated-result
# - bugprone-signed-char-misuse
# - bugprone-sizeof-expression
# - bugprone-switch-missing-default-case
# - bugprone-too-small-loop-variable
# - bugprone-unchecked-optional-access
# - clang-diagnostic-deprecated-declarations
# - clang-diagnostic-unused-function
# - google-runtime-int
# - google-runtime-references
# - modernize-avoid-bind
# - modernize-deprecated-headers
# - modernize-loop-convert
# - modernize-pass-by-value
# - modernize-raw-string-literal
# - modernize-return-braced-init-list
# - modernize-use-auto
# - modernize-use-default-member-init
# - modernize-use-emplace
# - modernize-use-equals-default
# - modernize-use-equals-delete
# - modernize-use-using
# - performance-avoid-endl
# - performance-no-automatic-move
# - performance-no-int-to-ptr
# - performance-noexcept-swap
# - performance-unnecessary-copy-initialization
# - performance-unnecessary-value-param
# - readability-else-after-return
# - readability-implicit-bool-conversion
# - readability-redundant-declaration
# - readability-redundant-string-cstr
#
Checks: '-*,
  abseil-*,
  -abseil-cleanup-ctad,
  -abseil-no-namespace,
  bugprone-*,
  -bugprone-assignment-in-if-condition,
  -bugprone-branch-clone,
  -bugprone-casting-through-void,
  -bugprone-crtp-constructor-accessibility,
  -bugprone-easily-swappable-parameters,
  -bugprone-empty-catch,
  -bugprone-exception-escape,
  -bugprone-implicit-widening-of-multiplication-result,
  -bugprone-inc-dec-in-conditions,
  -bugprone-infinite-loop,
  -bugprone-multi-level-implicit-pointer-conversion,
  -bugprone-narrowing-conversions,
  -bugprone-not-null-terminated-result,
  -bugprone-reserved-identifier,
  -bugprone-return-const-ref-from-parameter,
  -bugprone-signed-char-misuse,
  -bugprone-sizeof-expression,
  -bugprone-suspicious-stringview-data-usage,
  -bugprone-switch-missing-default-case,
  -bugprone-too-small-loop-variable,
  -bugprone-unchecked-optional-access,
  -bugprone-unused-local-non-trivial-variable,
  google-*,
  -google-readability-casting,
  -google-runtime-int,
  performance-*,
  -performance-avoid-endl,
  -performance-enum-size,
  -performance-inefficient-vector-operation,
  -performance-no-automatic-move,
  -performance-no-int-to-ptr,
  -performance-noexcept-swap,
  -performance-unnecessary-copy-initialization,
  -performance-unnecessary-value-param,
  clang-diagnostic-deprecated-register,
  clang-diagnostic-expansion-to-defined,
  clang-diagnostic-ignored-attributes,
  clang-diagnostic-non-pod-varargs,
  clang-diagnostic-shadow-field,
  clang-diagnostic-shift-sign-overflow,
  clang-diagnostic-tautological-undefined-compare,
  clang-diagnostic-thread-safety*,
  -clang-diagnostic-thread-safety-reference-return,
  clang-diagnostic-undefined-bool-conversion,
  clang-diagnostic-unreachable-code,
  clang-diagnostic-unreachable-code-loop-increment,
  clang-diagnostic-unused-const-variable,
  clang-diagnostic-unused-lambda-capture,
  clang-diagnostic-unused-local-typedef,
  clang-diagnostic-unused-private-field,
  clang-diagnostic-user-defined-warnings,
  misc-definitions-in-headers,
  misc-static-assert,
  misc-unconventional-assign-operator,
  misc-uniqueptr-reset-release,
  misc-unused-alias-decls,
  misc-unused-using-decls,
  modernize-make-shared,
  modernize-make-unique,
  modernize-replace-auto-ptr,
  modernize-replace-random-shuffle,
  modernize-shrink-to-fit,
  modernize-unary-static-assert,
  modernize-use-bool-literals,
  modernize-use-noexcept,
  modernize-use-nullptr,
  modernize-use-override,
  modernize-use-starts-ends-with,
  modernize-use-transparent-functors,
  readability-braces-around-statements,
  readability-const-return-type,
  readability-container-size-empty,
  readability-delete-null-pointer,
  readability-deleted-default,
  readability-duplicate-include,
  readability-function-size,
  readability-inconsistent-declaration-parameter-name,
  -readability-math-missing-parentheses,
  readability-misleading-indentation,
  readability-misplaced-array-index,
  readability-redundant-access-specifiers,
  readability-redundant-control-flow,
  readability-redundant-function-ptr-dereference,
  readability-redundant-smartptr-get,
  readability-redundant-string-init,
  readability-simplify-boolean-expr,
  readability-static-definition-in-anonymous-namespace,
  readability-string-compare,
  readability-uniqueptr-delete-release'
WarningsAsErrors: '*'
CheckOptions:
  - key:    bugprone-unused-return-value.AllowCastToVoid
    value:  true
  - key:    readability-braces-around-statements.ShortStatementLines
    value:  '450'
  - key:    readability-simplify-boolean-expr.SimplifyDeMorgan
    value:  false
  - key:    modernize-make-unique.MakeSmartPtrFunction
    value:  'absl::make_unique'
  - key:    modernize-make-unique.MakeSmartPtrFunctionHeader
    value:  'absl/memory/memory.h'
