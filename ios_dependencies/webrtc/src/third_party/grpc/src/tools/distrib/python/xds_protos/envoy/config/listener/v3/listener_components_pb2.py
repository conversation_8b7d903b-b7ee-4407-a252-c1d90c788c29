# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/listener/v3/listener_components.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.core.v3 import address_pb2 as envoy_dot_config_dot_core_dot_v3_dot_address__pb2
from envoy.config.core.v3 import base_pb2 as envoy_dot_config_dot_core_dot_v3_dot_base__pb2
from envoy.config.core.v3 import config_source_pb2 as envoy_dot_config_dot_core_dot_v3_dot_config__source__pb2
from envoy.type.v3 import range_pb2 as envoy_dot_type_dot_v3_dot_range__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from envoy.annotations import deprecation_pb2 as envoy_dot_annotations_dot_deprecation__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2envoy/config/listener/v3/listener_components.proto\x12\x18\x65nvoy.config.listener.v3\x1a\"envoy/config/core/v3/address.proto\x1a\x1f\x65nvoy/config/core/v3/base.proto\x1a(envoy/config/core/v3/config_source.proto\x1a\x19\x65nvoy/type/v3/range.proto\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a#envoy/annotations/deprecation.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\xde\x01\n\x06\x46ilter\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12,\n\x0ctyped_config\x18\x04 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x12G\n\x10\x63onfig_discovery\x18\x05 \x01(\x0b\x32+.envoy.config.core.v3.ExtensionConfigSourceH\x00:#\x9a\xc5\x88\x1e\x1e\n\x1c\x65nvoy.api.v2.listener.FilterB\r\n\x0b\x63onfig_typeJ\x04\x08\x03\x10\x04J\x04\x08\x02\x10\x03R\x06\x63onfig\"\xb4\x05\n\x10\x46ilterChainMatch\x12\x43\n\x10\x64\x65stination_port\x18\x08 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x0b\xfa\x42\x08*\x06\x18\xff\xff\x03(\x01\x12\x36\n\rprefix_ranges\x18\x03 \x03(\x0b\x32\x1f.envoy.config.core.v3.CidrRange\x12\x16\n\x0e\x61\x64\x64ress_suffix\x18\x04 \x01(\t\x12\x30\n\nsuffix_len\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12\x44\n\x1b\x64irect_source_prefix_ranges\x18\r \x03(\x0b\x32\x1f.envoy.config.core.v3.CidrRange\x12^\n\x0bsource_type\x18\x0c \x01(\x0e\x32?.envoy.config.listener.v3.FilterChainMatch.ConnectionSourceTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12=\n\x14source_prefix_ranges\x18\x06 \x03(\x0b\x32\x1f.envoy.config.core.v3.CidrRange\x12&\n\x0csource_ports\x18\x07 \x03(\rB\x10\xfa\x42\r\x92\x01\n\"\x08*\x06\x18\xff\xff\x03(\x01\x12\x14\n\x0cserver_names\x18\x0b \x03(\t\x12\x1a\n\x12transport_protocol\x18\t \x01(\t\x12\x1d\n\x15\x61pplication_protocols\x18\n \x03(\t\"F\n\x14\x43onnectionSourceType\x12\x07\n\x03\x41NY\x10\x00\x12\x17\n\x13SAME_IP_OR_LOOPBACK\x10\x01\x12\x0c\n\x08\x45XTERNAL\x10\x02:-\x9a\xc5\x88\x1e(\n&envoy.api.v2.listener.FilterChainMatchJ\x04\x08\x01\x10\x02\"\xec\x03\n\x0b\x46ilterChain\x12\x46\n\x12\x66ilter_chain_match\x18\x01 \x01(\x0b\x32*.envoy.config.listener.v3.FilterChainMatch\x12\x31\n\x07\x66ilters\x18\x03 \x03(\x0b\x32 .envoy.config.listener.v3.Filter\x12@\n\x0fuse_proxy_proto\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.BoolValueB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12\x30\n\x08metadata\x18\x05 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x12?\n\x10transport_socket\x18\x06 \x01(\x0b\x32%.envoy.config.core.v3.TransportSocket\x12\x43\n transport_socket_connect_timeout\x18\t \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x0c\n\x04name\x18\x07 \x01(\t:(\x9a\xc5\x88\x1e#\n!envoy.api.v2.listener.FilterChainJ\x04\x08\x02\x10\x03J\x04\x08\x08\x10\tR\x0btls_contextR\x17on_demand_configuration\"\xfe\x04\n!ListenerFilterChainMatchPredicate\x12X\n\x08or_match\x18\x01 \x01(\x0b\x32\x44.envoy.config.listener.v3.ListenerFilterChainMatchPredicate.MatchSetH\x00\x12Y\n\tand_match\x18\x02 \x01(\x0b\x32\x44.envoy.config.listener.v3.ListenerFilterChainMatchPredicate.MatchSetH\x00\x12P\n\tnot_match\x18\x03 \x01(\x0b\x32;.envoy.config.listener.v3.ListenerFilterChainMatchPredicateH\x00\x12\x1c\n\tany_match\x18\x04 \x01(\x08\x42\x07\xfa\x42\x04j\x02\x08\x01H\x00\x12;\n\x16\x64\x65stination_port_range\x18\x05 \x01(\x0b\x32\x19.envoy.type.v3.Int32RangeH\x00\x1a\xa9\x01\n\x08MatchSet\x12T\n\x05rules\x18\x01 \x03(\x0b\x32;.envoy.config.listener.v3.ListenerFilterChainMatchPredicateB\x08\xfa\x42\x05\x92\x01\x02\x08\x02:G\x9a\xc5\x88\x1e\x42\<EMAIL>:>\x9a\xc5\x88\x1e\x39\n7envoy.api.v2.listener.ListenerFilterChainMatchPredicateB\x0b\n\x04rule\x12\x03\xf8\x42\x01\"\xbe\x02\n\x0eListenerFilter\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x12G\n\x10\x63onfig_discovery\x18\x05 \x01(\x0b\x32+.envoy.config.core.v3.ExtensionConfigSourceH\x00\x12T\n\x0f\x66ilter_disabled\x18\x04 \x01(\x0b\x32;.envoy.config.listener.v3.ListenerFilterChainMatchPredicate:+\x9a\xc5\x88\x1e&\n$envoy.api.v2.listener.ListenerFilterB\r\n\x0b\x63onfig_typeJ\x04\x08\x02\x10\x03R\x06\x63onfigB\x97\x01\n&io.envoyproxy.envoy.config.listener.v3B\x17ListenerComponentsProtoP\x01ZJgithub.com/envoyproxy/go-control-plane/envoy/config/listener/v3;listenerv3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.listener.v3.listener_components_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n&io.envoyproxy.envoy.config.listener.v3B\027ListenerComponentsProtoP\001ZJgithub.com/envoyproxy/go-control-plane/envoy/config/listener/v3;listenerv3\272\200\310\321\006\002\020\002'
  _FILTER.fields_by_name['name']._options = None
  _FILTER.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _FILTER._options = None
  _FILTER._serialized_options = b'\232\305\210\036\036\n\034envoy.api.v2.listener.Filter'
  _FILTERCHAINMATCH.fields_by_name['destination_port']._options = None
  _FILTERCHAINMATCH.fields_by_name['destination_port']._serialized_options = b'\372B\010*\006\030\377\377\003(\001'
  _FILTERCHAINMATCH.fields_by_name['source_type']._options = None
  _FILTERCHAINMATCH.fields_by_name['source_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _FILTERCHAINMATCH.fields_by_name['source_ports']._options = None
  _FILTERCHAINMATCH.fields_by_name['source_ports']._serialized_options = b'\372B\r\222\001\n\"\010*\006\030\377\377\003(\001'
  _FILTERCHAINMATCH._options = None
  _FILTERCHAINMATCH._serialized_options = b'\232\305\210\036(\n&envoy.api.v2.listener.FilterChainMatch'
  _FILTERCHAIN.fields_by_name['use_proxy_proto']._options = None
  _FILTERCHAIN.fields_by_name['use_proxy_proto']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _FILTERCHAIN._options = None
  _FILTERCHAIN._serialized_options = b'\232\305\210\036#\n!envoy.api.v2.listener.FilterChain'
  _LISTENERFILTERCHAINMATCHPREDICATE_MATCHSET.fields_by_name['rules']._options = None
  _LISTENERFILTERCHAINMATCHPREDICATE_MATCHSET.fields_by_name['rules']._serialized_options = b'\372B\005\222\001\002\010\002'
  _LISTENERFILTERCHAINMATCHPREDICATE_MATCHSET._options = None
  _LISTENERFILTERCHAINMATCHPREDICATE_MATCHSET._serialized_options = b'\232\305\210\036B\<EMAIL>'
  _LISTENERFILTERCHAINMATCHPREDICATE.oneofs_by_name['rule']._options = None
  _LISTENERFILTERCHAINMATCHPREDICATE.oneofs_by_name['rule']._serialized_options = b'\370B\001'
  _LISTENERFILTERCHAINMATCHPREDICATE.fields_by_name['any_match']._options = None
  _LISTENERFILTERCHAINMATCHPREDICATE.fields_by_name['any_match']._serialized_options = b'\372B\004j\002\010\001'
  _LISTENERFILTERCHAINMATCHPREDICATE._options = None
  _LISTENERFILTERCHAINMATCHPREDICATE._serialized_options = b'\232\305\210\0369\n7envoy.api.v2.listener.ListenerFilterChainMatchPredicate'
  _LISTENERFILTER.fields_by_name['name']._options = None
  _LISTENERFILTER.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _LISTENERFILTER._options = None
  _LISTENERFILTER._serialized_options = b'\232\305\210\036&\n$envoy.api.v2.listener.ListenerFilter'
  _globals['_FILTER']._serialized_start=438
  _globals['_FILTER']._serialized_end=660
  _globals['_FILTERCHAINMATCH']._serialized_start=663
  _globals['_FILTERCHAINMATCH']._serialized_end=1355
  _globals['_FILTERCHAINMATCH_CONNECTIONSOURCETYPE']._serialized_start=1232
  _globals['_FILTERCHAINMATCH_CONNECTIONSOURCETYPE']._serialized_end=1302
  _globals['_FILTERCHAIN']._serialized_start=1358
  _globals['_FILTERCHAIN']._serialized_end=1850
  _globals['_LISTENERFILTERCHAINMATCHPREDICATE']._serialized_start=1853
  _globals['_LISTENERFILTERCHAINMATCHPREDICATE']._serialized_end=2491
  _globals['_LISTENERFILTERCHAINMATCHPREDICATE_MATCHSET']._serialized_start=2245
  _globals['_LISTENERFILTERCHAINMATCHPREDICATE_MATCHSET']._serialized_end=2414
  _globals['_LISTENERFILTER']._serialized_start=2494
  _globals['_LISTENERFILTER']._serialized_end=2812
# @@protoc_insertion_point(module_scope)
