# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/health_check/v2/health_check.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from envoy.type import percent_pb2 as envoy_dot_type_dot_percent__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n;envoy/config/filter/http/health_check/v2/health_check.proto\x12(envoy.config.filter.http.health_check.v2\x1a)envoy/api/v2/route/route_components.proto\x1a\x18\x65nvoy/type/percent.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x94\x03\n\x0bHealthCheck\x12?\n\x11pass_through_mode\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.BoolValueB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12-\n\ncache_time\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x80\x01\n\x1f\x63luster_min_healthy_percentages\x18\x04 \x03(\x0b\x32W.envoy.config.filter.http.health_check.v2.HealthCheck.ClusterMinHealthyPercentagesEntry\x12\x32\n\x07headers\x18\x05 \x03(\x0b\x32!.envoy.api.v2.route.HeaderMatcher\x1aX\n!ClusterMinHealthyPercentagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\"\n\x05value\x18\x02 \x01(\x0b\x32\x13.envoy.type.Percent:\x02\x38\x01J\x04\x08\x02\x10\x03\x42\xe9\x01\n6io.envoyproxy.envoy.config.filter.http.health_check.v2B\x10HealthCheckProtoP\x01Z^github.com/envoyproxy/go-control-plane/envoy/config/filter/http/health_check/v2;health_checkv2\xf2\x98\xfe\x8f\x05/\x12-envoy.extensions.filters.http.health_check.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.health_check.v2.health_check_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n6io.envoyproxy.envoy.config.filter.http.health_check.v2B\020HealthCheckProtoP\001Z^github.com/envoyproxy/go-control-plane/envoy/config/filter/http/health_check/v2;health_checkv2\362\230\376\217\005/\022-envoy.extensions.filters.http.health_check.v3\272\200\310\321\006\002\020\001'
  _HEALTHCHECK_CLUSTERMINHEALTHYPERCENTAGESENTRY._options = None
  _HEALTHCHECK_CLUSTERMINHEALTHYPERCENTAGESENTRY._serialized_options = b'8\001'
  _HEALTHCHECK.fields_by_name['pass_through_mode']._options = None
  _HEALTHCHECK.fields_by_name['pass_through_mode']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_HEALTHCHECK']._serialized_start=327
  _globals['_HEALTHCHECK']._serialized_end=731
  _globals['_HEALTHCHECK_CLUSTERMINHEALTHYPERCENTAGESENTRY']._serialized_start=637
  _globals['_HEALTHCHECK_CLUSTERMINHEALTHYPERCENTAGESENTRY']._serialized_end=725
# @@protoc_insertion_point(module_scope)
