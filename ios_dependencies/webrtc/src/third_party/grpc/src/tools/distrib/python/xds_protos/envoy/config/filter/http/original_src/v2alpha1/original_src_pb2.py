# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/original_src/v2alpha1/original_src.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAenvoy/config/filter/http/original_src/v2alpha1/original_src.proto\x12.envoy.config.filter.http.original_src.v2alpha1\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"\x1b\n\x0bOriginalSrc\x12\x0c\n\x04mark\x18\x01 \x01(\rB\xe6\x01\n<io.envoyproxy.envoy.config.filter.http.original_src.v2alpha1B\x10OriginalSrcProtoP\x01ZUgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/original_src/v2alpha1\xf2\x98\xfe\x8f\x05/\x12-envoy.extensions.filters.http.original_src.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.original_src.v2alpha1.original_src_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n<io.envoyproxy.envoy.config.filter.http.original_src.v2alpha1B\020OriginalSrcProtoP\001ZUgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/original_src/v2alpha1\362\230\376\217\005/\022-envoy.extensions.filters.http.original_src.v3\272\200\310\321\006\002\020\001'
  _globals['_ORIGINALSRC']._serialized_start=180
  _globals['_ORIGINALSRC']._serialized_end=207
# @@protoc_insertion_point(module_scope)
