# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/common/matcher/v3/matcher.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.core.v3 import extension_pb2 as envoy_dot_config_dot_core_dot_v3_dot_extension__pb2
from envoy.config.route.v3 import route_components_pb2 as envoy_dot_config_dot_route_dot_v3_dot_route__components__pb2
from envoy.type.matcher.v3 import string_pb2 as envoy_dot_type_dot_matcher_dot_v3_dot_string__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,envoy/config/common/matcher/v3/matcher.proto\x12\x1e\x65nvoy.config.common.matcher.v3\x1a$envoy/config/core/v3/extension.proto\x1a,envoy/config/route/v3/route_components.proto\x1a\"envoy/type/matcher/v3/string.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xe3\x0f\n\x07Matcher\x12K\n\x0cmatcher_list\x18\x01 \x01(\x0b\x32\x33.envoy.config.common.matcher.v3.Matcher.MatcherListH\x00\x12K\n\x0cmatcher_tree\x18\x02 \x01(\x0b\x32\x33.envoy.config.common.matcher.v3.Matcher.MatcherTreeH\x00\x12\x44\n\x0bon_no_match\x18\x03 \x01(\x0b\x32/.envoy.config.common.matcher.v3.Matcher.OnMatch\x1a\x94\x01\n\x07OnMatch\x12:\n\x07matcher\x18\x01 \x01(\x0b\x32\'.envoy.config.common.matcher.v3.MatcherH\x00\x12<\n\x06\x61\x63tion\x18\x02 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigH\x00\x42\x0f\n\x08on_match\x12\x03\xf8\x42\x01\x1a\xa5\x08\n\x0bMatcherList\x12\\\n\x08matchers\x18\x01 \x03(\x0b\<EMAIL>\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1a\xfd\x05\n\tPredicate\x12i\n\x10single_predicate\x18\x01 \x01(\x0b\x32M.envoy.config.common.matcher.v3.Matcher.MatcherList.Predicate.SinglePredicateH\x00\x12\x61\n\nor_matcher\x18\x02 \x01(\x0b\x32K.envoy.config.common.matcher.v3.Matcher.MatcherList.Predicate.PredicateListH\x00\x12\x62\n\x0b\x61nd_matcher\x18\x03 \x01(\x0b\x32K.envoy.config.common.matcher.v3.Matcher.MatcherList.Predicate.PredicateListH\x00\x12T\n\x0bnot_matcher\x18\x04 \x01(\x0b\x32=.envoy.config.common.matcher.v3.Matcher.MatcherList.PredicateH\x00\x1a\xe7\x01\n\x0fSinglePredicate\x12\x43\n\x05input\x18\x01 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12;\n\x0bvalue_match\x18\x02 \x01(\x0b\x32$.envoy.type.matcher.v3.StringMatcherH\x00\x12\x42\n\x0c\x63ustom_match\x18\x03 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigH\x00\x42\x0e\n\x07matcher\x12\x03\xf8\x42\x01\x1ak\n\rPredicateList\x12Z\n\tpredicate\x18\x01 \x03(\x0b\x32=.envoy.config.common.matcher.v3.Matcher.MatcherList.PredicateB\x08\xfa\x42\x05\x92\x01\x02\x08\x02\x42\x11\n\nmatch_type\x12\x03\xf8\x42\x01\x1a\xb7\x01\n\x0c\x46ieldMatcher\x12Z\n\tpredicate\x18\x01 \x01(\x0b\x32=.envoy.config.common.matcher.v3.Matcher.MatcherList.PredicateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12K\n\x08on_match\x18\x02 \x01(\x0b\x32/.envoy.config.common.matcher.v3.Matcher.OnMatchB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x1a\xa3\x04\n\x0bMatcherTree\x12\x43\n\x05input\x18\x01 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12W\n\x0f\x65xact_match_map\x18\x02 \x01(\x0b\x32<.envoy.config.common.matcher.v3.Matcher.MatcherTree.MatchMapH\x00\x12X\n\x10prefix_match_map\x18\x03 \x01(\x0b\x32<.envoy.config.common.matcher.v3.Matcher.MatcherTree.MatchMapH\x00\x12\x42\n\x0c\x63ustom_match\x18\x04 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigH\x00\x1a\xc5\x01\n\x08MatchMap\x12\\\n\x03map\x18\x01 \x03(\x0b\x32\x45.envoy.config.common.matcher.v3.Matcher.MatcherTree.MatchMap.MapEntryB\x08\xfa\x42\x05\x9a\x01\x02\x08\x01\x1a[\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12>\n\x05value\x18\x02 \x01(\x0b\x32/.envoy.config.common.matcher.v3.Matcher.OnMatch:\x02\x38\x01\x42\x10\n\ttree_type\x12\x03\xf8\x42\x01\x42\x13\n\x0cmatcher_type\x12\x03\xf8\x42\x01\"\x97\x07\n\x0eMatchPredicate\x12K\n\x08or_match\x18\x01 \x01(\x0b\x32\x37.envoy.config.common.matcher.v3.MatchPredicate.MatchSetH\x00\x12L\n\tand_match\x18\x02 \x01(\x0b\x32\x37.envoy.config.common.matcher.v3.MatchPredicate.MatchSetH\x00\x12\x43\n\tnot_match\x18\x03 \x01(\x0b\x32..envoy.config.common.matcher.v3.MatchPredicateH\x00\x12\x1c\n\tany_match\x18\x04 \x01(\x08\x42\x07\xfa\x42\x04j\x02\x08\x01H\x00\x12V\n\x1ahttp_request_headers_match\x18\x05 \x01(\x0b\x32\x30.envoy.config.common.matcher.v3.HttpHeadersMatchH\x00\x12W\n\x1bhttp_request_trailers_match\x18\x06 \x01(\x0b\x32\x30.envoy.config.common.matcher.v3.HttpHeadersMatchH\x00\x12W\n\x1bhttp_response_headers_match\x18\x07 \x01(\x0b\x32\x30.envoy.config.common.matcher.v3.HttpHeadersMatchH\x00\x12X\n\x1chttp_response_trailers_match\x18\x08 \x01(\x0b\x32\x30.envoy.config.common.matcher.v3.HttpHeadersMatchH\x00\x12_\n\x1fhttp_request_generic_body_match\x18\t \x01(\x0b\x32\x34.envoy.config.common.matcher.v3.HttpGenericBodyMatchH\x00\x12`\n http_response_generic_body_match\x18\n \x01(\x0b\x32\x34.envoy.config.common.matcher.v3.HttpGenericBodyMatchH\x00\x1aS\n\x08MatchSet\x12G\n\x05rules\x18\x01 \x03(\x0b\x32..envoy.config.common.matcher.v3.MatchPredicateB\x08\xfa\x42\x05\x92\x01\x02\x08\x02\x42\x0b\n\x04rule\x12\x03\xf8\x42\x01\"I\n\x10HttpHeadersMatch\x12\x35\n\x07headers\x18\x01 \x03(\x0b\x32$.envoy.config.route.v3.HeaderMatcher\"\xf1\x01\n\x14HttpGenericBodyMatch\x12\x13\n\x0b\x62ytes_limit\x18\x01 \x01(\r\x12\x61\n\x08patterns\x18\x02 \x03(\x0b\x32\x45.envoy.config.common.matcher.v3.HttpGenericBodyMatch.GenericTextMatchB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1a\x61\n\x10GenericTextMatch\x12\x1f\n\x0cstring_match\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01H\x00\x12\x1f\n\x0c\x62inary_match\x18\x02 \x01(\x0c\x42\x07\xfa\x42\x04z\x02\x10\x01H\x00\x42\x0b\n\x04rule\x12\x03\xf8\x42\x01\x42\x97\x01\n,io.envoyproxy.envoy.config.common.matcher.v3B\x0cMatcherProtoP\x01ZOgithub.com/envoyproxy/go-control-plane/envoy/config/common/matcher/v3;matcherv3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.common.matcher.v3.matcher_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n,io.envoyproxy.envoy.config.common.matcher.v3B\014MatcherProtoP\001ZOgithub.com/envoyproxy/go-control-plane/envoy/config/common/matcher/v3;matcherv3\272\200\310\321\006\002\020\002'
  _MATCHER_ONMATCH.oneofs_by_name['on_match']._options = None
  _MATCHER_ONMATCH.oneofs_by_name['on_match']._serialized_options = b'\370B\001'
  _MATCHER_MATCHERLIST_PREDICATE_SINGLEPREDICATE.oneofs_by_name['matcher']._options = None
  _MATCHER_MATCHERLIST_PREDICATE_SINGLEPREDICATE.oneofs_by_name['matcher']._serialized_options = b'\370B\001'
  _MATCHER_MATCHERLIST_PREDICATE_SINGLEPREDICATE.fields_by_name['input']._options = None
  _MATCHER_MATCHERLIST_PREDICATE_SINGLEPREDICATE.fields_by_name['input']._serialized_options = b'\372B\005\212\001\002\020\001'
  _MATCHER_MATCHERLIST_PREDICATE_PREDICATELIST.fields_by_name['predicate']._options = None
  _MATCHER_MATCHERLIST_PREDICATE_PREDICATELIST.fields_by_name['predicate']._serialized_options = b'\372B\005\222\001\002\010\002'
  _MATCHER_MATCHERLIST_PREDICATE.oneofs_by_name['match_type']._options = None
  _MATCHER_MATCHERLIST_PREDICATE.oneofs_by_name['match_type']._serialized_options = b'\370B\001'
  _MATCHER_MATCHERLIST_FIELDMATCHER.fields_by_name['predicate']._options = None
  _MATCHER_MATCHERLIST_FIELDMATCHER.fields_by_name['predicate']._serialized_options = b'\372B\005\212\001\002\020\001'
  _MATCHER_MATCHERLIST_FIELDMATCHER.fields_by_name['on_match']._options = None
  _MATCHER_MATCHERLIST_FIELDMATCHER.fields_by_name['on_match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _MATCHER_MATCHERLIST.fields_by_name['matchers']._options = None
  _MATCHER_MATCHERLIST.fields_by_name['matchers']._serialized_options = b'\372B\005\222\001\002\010\001'
  _MATCHER_MATCHERTREE_MATCHMAP_MAPENTRY._options = None
  _MATCHER_MATCHERTREE_MATCHMAP_MAPENTRY._serialized_options = b'8\001'
  _MATCHER_MATCHERTREE_MATCHMAP.fields_by_name['map']._options = None
  _MATCHER_MATCHERTREE_MATCHMAP.fields_by_name['map']._serialized_options = b'\372B\005\232\001\002\010\001'
  _MATCHER_MATCHERTREE.oneofs_by_name['tree_type']._options = None
  _MATCHER_MATCHERTREE.oneofs_by_name['tree_type']._serialized_options = b'\370B\001'
  _MATCHER_MATCHERTREE.fields_by_name['input']._options = None
  _MATCHER_MATCHERTREE.fields_by_name['input']._serialized_options = b'\372B\005\212\001\002\020\001'
  _MATCHER.oneofs_by_name['matcher_type']._options = None
  _MATCHER.oneofs_by_name['matcher_type']._serialized_options = b'\370B\001'
  _MATCHPREDICATE_MATCHSET.fields_by_name['rules']._options = None
  _MATCHPREDICATE_MATCHSET.fields_by_name['rules']._serialized_options = b'\372B\005\222\001\002\010\002'
  _MATCHPREDICATE.oneofs_by_name['rule']._options = None
  _MATCHPREDICATE.oneofs_by_name['rule']._serialized_options = b'\370B\001'
  _MATCHPREDICATE.fields_by_name['any_match']._options = None
  _MATCHPREDICATE.fields_by_name['any_match']._serialized_options = b'\372B\004j\002\010\001'
  _HTTPGENERICBODYMATCH_GENERICTEXTMATCH.oneofs_by_name['rule']._options = None
  _HTTPGENERICBODYMATCH_GENERICTEXTMATCH.oneofs_by_name['rule']._serialized_options = b'\370B\001'
  _HTTPGENERICBODYMATCH_GENERICTEXTMATCH.fields_by_name['string_match']._options = None
  _HTTPGENERICBODYMATCH_GENERICTEXTMATCH.fields_by_name['string_match']._serialized_options = b'\372B\004r\002\020\001'
  _HTTPGENERICBODYMATCH_GENERICTEXTMATCH.fields_by_name['binary_match']._options = None
  _HTTPGENERICBODYMATCH_GENERICTEXTMATCH.fields_by_name['binary_match']._serialized_options = b'\372B\004z\002\020\001'
  _HTTPGENERICBODYMATCH.fields_by_name['patterns']._options = None
  _HTTPGENERICBODYMATCH.fields_by_name['patterns']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_MATCHER']._serialized_start=257
  _globals['_MATCHER']._serialized_end=2276
  _globals['_MATCHER_ONMATCH']._serialized_start=493
  _globals['_MATCHER_ONMATCH']._serialized_end=641
  _globals['_MATCHER_MATCHERLIST']._serialized_start=644
  _globals['_MATCHER_MATCHERLIST']._serialized_end=1705
  _globals['_MATCHER_MATCHERLIST_PREDICATE']._serialized_start=754
  _globals['_MATCHER_MATCHERLIST_PREDICATE']._serialized_end=1519
  _globals['_MATCHER_MATCHERLIST_PREDICATE_SINGLEPREDICATE']._serialized_start=1160
  _globals['_MATCHER_MATCHERLIST_PREDICATE_SINGLEPREDICATE']._serialized_end=1391
  _globals['_MATCHER_MATCHERLIST_PREDICATE_PREDICATELIST']._serialized_start=1393
  _globals['_MATCHER_MATCHERLIST_PREDICATE_PREDICATELIST']._serialized_end=1500
  _globals['_MATCHER_MATCHERLIST_FIELDMATCHER']._serialized_start=1522
  _globals['_MATCHER_MATCHERLIST_FIELDMATCHER']._serialized_end=1705
  _globals['_MATCHER_MATCHERTREE']._serialized_start=1708
  _globals['_MATCHER_MATCHERTREE']._serialized_end=2255
  _globals['_MATCHER_MATCHERTREE_MATCHMAP']._serialized_start=2040
  _globals['_MATCHER_MATCHERTREE_MATCHMAP']._serialized_end=2237
  _globals['_MATCHER_MATCHERTREE_MATCHMAP_MAPENTRY']._serialized_start=2146
  _globals['_MATCHER_MATCHERTREE_MATCHMAP_MAPENTRY']._serialized_end=2237
  _globals['_MATCHPREDICATE']._serialized_start=2279
  _globals['_MATCHPREDICATE']._serialized_end=3198
  _globals['_MATCHPREDICATE_MATCHSET']._serialized_start=3102
  _globals['_MATCHPREDICATE_MATCHSET']._serialized_end=3185
  _globals['_HTTPHEADERSMATCH']._serialized_start=3200
  _globals['_HTTPHEADERSMATCH']._serialized_end=3273
  _globals['_HTTPGENERICBODYMATCH']._serialized_start=3276
  _globals['_HTTPGENERICBODYMATCH']._serialized_end=3517
  _globals['_HTTPGENERICBODYMATCH_GENERICTEXTMATCH']._serialized_start=3420
  _globals['_HTTPGENERICBODYMATCH_GENERICTEXTMATCH']._serialized_end=3517
# @@protoc_insertion_point(module_scope)
