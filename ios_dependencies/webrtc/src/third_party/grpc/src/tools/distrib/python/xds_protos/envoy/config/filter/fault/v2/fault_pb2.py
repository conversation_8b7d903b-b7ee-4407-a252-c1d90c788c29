# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/fault/v2/fault.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.type import percent_pb2 as envoy_dot_type_dot_percent__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from envoy.annotations import deprecation_pb2 as envoy_dot_annotations_dot_deprecation__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(envoy/config/filter/fault/v2/fault.proto\x12\x1c\x65nvoy.config.filter.fault.v2\x1a\x18\x65nvoy/type/percent.proto\x1a\x1egoogle/protobuf/duration.proto\x1a#envoy/annotations/deprecation.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xe9\x02\n\nFaultDelay\x12O\n\x04type\x18\x01 \x01(\x0e\x32\x37.envoy.config.filter.fault.v2.FaultDelay.FaultDelayTypeB\x08\x18\x01\xb8\xee\xf2\xd2\x05\x01\x12:\n\x0b\x66ixed_delay\x18\x03 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\xfa\x42\x05\xaa\x01\x02*\x00H\x00\x12L\n\x0cheader_delay\x18\x05 \x01(\x0b\x32\x34.envoy.config.filter.fault.v2.FaultDelay.HeaderDelayH\x00\x12\x31\n\npercentage\x18\x04 \x01(\x0b\x32\x1d.envoy.type.FractionalPercent\x1a\r\n\x0bHeaderDelay\"\x1b\n\x0e\x46\x61ultDelayType\x12\t\n\x05\x46IXED\x10\x00\x42\x1b\n\x14\x66\x61ult_delay_secifier\x12\x03\xf8\x42\x01J\x04\x08\x02\x10\x03\"\xb2\x02\n\x0e\x46\x61ultRateLimit\x12N\n\x0b\x66ixed_limit\x18\x01 \x01(\x0b\x32\x37.envoy.config.filter.fault.v2.FaultRateLimit.FixedLimitH\x00\x12P\n\x0cheader_limit\x18\x03 \x01(\x0b\x32\x38.envoy.config.filter.fault.v2.FaultRateLimit.HeaderLimitH\x00\x12\x31\n\npercentage\x18\x02 \x01(\x0b\x32\x1d.envoy.type.FractionalPercent\x1a)\n\nFixedLimit\x12\x1b\n\nlimit_kbps\x18\x01 \x01(\x04\x42\x07\xfa\x42\x04\x32\x02(\x01\x1a\r\n\x0bHeaderLimitB\x11\n\nlimit_type\x12\x03\xf8\x42\x01\x42\xbf\x01\n*io.envoyproxy.envoy.config.filter.fault.v2B\nFaultProtoP\x01ZKgithub.com/envoyproxy/go-control-plane/envoy/config/filter/fault/v2;faultv2\xf2\x98\xfe\x8f\x05*\x12(envoy.extensions.filters.common.fault.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.fault.v2.fault_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*io.envoyproxy.envoy.config.filter.fault.v2B\nFaultProtoP\001ZKgithub.com/envoyproxy/go-control-plane/envoy/config/filter/fault/v2;faultv2\362\230\376\217\005*\022(envoy.extensions.filters.common.fault.v3\272\200\310\321\006\002\020\001'
  _FAULTDELAY.oneofs_by_name['fault_delay_secifier']._options = None
  _FAULTDELAY.oneofs_by_name['fault_delay_secifier']._serialized_options = b'\370B\001'
  _FAULTDELAY.fields_by_name['type']._options = None
  _FAULTDELAY.fields_by_name['type']._serialized_options = b'\030\001\270\356\362\322\005\001'
  _FAULTDELAY.fields_by_name['fixed_delay']._options = None
  _FAULTDELAY.fields_by_name['fixed_delay']._serialized_options = b'\372B\005\252\001\002*\000'
  _FAULTRATELIMIT_FIXEDLIMIT.fields_by_name['limit_kbps']._options = None
  _FAULTRATELIMIT_FIXEDLIMIT.fields_by_name['limit_kbps']._serialized_options = b'\372B\0042\002(\001'
  _FAULTRATELIMIT.oneofs_by_name['limit_type']._options = None
  _FAULTRATELIMIT.oneofs_by_name['limit_type']._serialized_options = b'\370B\001'
  _globals['_FAULTDELAY']._serialized_start=258
  _globals['_FAULTDELAY']._serialized_end=619
  _globals['_FAULTDELAY_HEADERDELAY']._serialized_start=542
  _globals['_FAULTDELAY_HEADERDELAY']._serialized_end=555
  _globals['_FAULTDELAY_FAULTDELAYTYPE']._serialized_start=557
  _globals['_FAULTDELAY_FAULTDELAYTYPE']._serialized_end=584
  _globals['_FAULTRATELIMIT']._serialized_start=622
  _globals['_FAULTRATELIMIT']._serialized_end=928
  _globals['_FAULTRATELIMIT_FIXEDLIMIT']._serialized_start=853
  _globals['_FAULTRATELIMIT_FIXEDLIMIT']._serialized_end=894
  _globals['_FAULTRATELIMIT_HEADERLIMIT']._serialized_start=896
  _globals['_FAULTRATELIMIT_HEADERLIMIT']._serialized_end=909
# @@protoc_insertion_point(module_scope)
