# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/overload/v2alpha/overload.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,envoy/config/overload/v2alpha/overload.proto\x12\x1d\x65nvoy.config.overload.v2alpha\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x94\x01\n\x0fResourceMonitor\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12-\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x17.google.protobuf.StructB\x02\x18\x01H\x00\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\r\n\x0b\x63onfig_type\":\n\x10ThresholdTrigger\x12&\n\x05value\x18\x01 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x19\x00\x00\x00\x00\x00\x00\xf0?)\x00\x00\x00\x00\x00\x00\x00\x00\"|\n\x07Trigger\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x44\n\tthreshold\x18\x02 \x01(\x0b\x32/.envoy.config.overload.v2alpha.ThresholdTriggerH\x00\x42\x14\n\rtrigger_oneof\x12\x03\xf8\x42\x01\"k\n\x0eOverloadAction\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x42\n\x08triggers\x18\x02 \x03(\x0b\x32&.envoy.config.overload.v2alpha.TriggerB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\"\xdb\x01\n\x0fOverloadManager\x12\x33\n\x10refresh_interval\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\x12S\n\x11resource_monitors\x18\x02 \x03(\x0b\x32..envoy.config.overload.v2alpha.ResourceMonitorB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12>\n\x07\x61\x63tions\x18\x03 \x03(\x0b\x32-.envoy.config.overload.v2alpha.OverloadActionB\x8c\x01\n+io.envoyproxy.envoy.config.overload.v2alphaB\rOverloadProtoP\x01ZDgithub.com/envoyproxy/go-control-plane/envoy/config/overload/v2alpha\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.overload.v2alpha.overload_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n+io.envoyproxy.envoy.config.overload.v2alphaB\rOverloadProtoP\001ZDgithub.com/envoyproxy/go-control-plane/envoy/config/overload/v2alpha\272\200\310\321\006\002\020\001'
  _RESOURCEMONITOR.fields_by_name['name']._options = None
  _RESOURCEMONITOR.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _RESOURCEMONITOR.fields_by_name['config']._options = None
  _RESOURCEMONITOR.fields_by_name['config']._serialized_options = b'\030\001'
  _THRESHOLDTRIGGER.fields_by_name['value']._options = None
  _THRESHOLDTRIGGER.fields_by_name['value']._serialized_options = b'\372B\024\022\022\031\000\000\000\000\000\000\360?)\000\000\000\000\000\000\000\000'
  _TRIGGER.oneofs_by_name['trigger_oneof']._options = None
  _TRIGGER.oneofs_by_name['trigger_oneof']._serialized_options = b'\370B\001'
  _TRIGGER.fields_by_name['name']._options = None
  _TRIGGER.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _OVERLOADACTION.fields_by_name['name']._options = None
  _OVERLOADACTION.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _OVERLOADACTION.fields_by_name['triggers']._options = None
  _OVERLOADACTION.fields_by_name['triggers']._serialized_options = b'\372B\005\222\001\002\010\001'
  _OVERLOADMANAGER.fields_by_name['resource_monitors']._options = None
  _OVERLOADMANAGER.fields_by_name['resource_monitors']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_RESOURCEMONITOR']._serialized_start=225
  _globals['_RESOURCEMONITOR']._serialized_end=373
  _globals['_THRESHOLDTRIGGER']._serialized_start=375
  _globals['_THRESHOLDTRIGGER']._serialized_end=433
  _globals['_TRIGGER']._serialized_start=435
  _globals['_TRIGGER']._serialized_end=559
  _globals['_OVERLOADACTION']._serialized_start=561
  _globals['_OVERLOADACTION']._serialized_end=668
  _globals['_OVERLOADMANAGER']._serialized_start=671
  _globals['_OVERLOADMANAGER']._serialized_end=890
# @@protoc_insertion_point(module_scope)
