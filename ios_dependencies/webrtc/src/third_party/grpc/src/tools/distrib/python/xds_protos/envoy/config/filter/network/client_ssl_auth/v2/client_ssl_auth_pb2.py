# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/client_ssl_auth/v2/client_ssl_auth.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import address_pb2 as envoy_dot_api_dot_v2_dot_core_dot_address__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nDenvoy/config/filter/network/client_ssl_auth/v2/client_ssl_auth.proto\x12.envoy.config.filter.network.client_ssl_auth.v2\x1a\x1f\x65nvoy/api/v2/core/address.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xb7\x01\n\rClientSSLAuth\x12!\n\x10\x61uth_api_cluster\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x1c\n\x0bstat_prefix\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x30\n\rrefresh_delay\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x33\n\rip_white_list\x18\x04 \x03(\x0b\x32\x1c.envoy.api.v2.core.CidrRangeB\x80\x02\n<io.envoyproxy.envoy.config.filter.network.client_ssl_auth.v2B\x12\x43lientSslAuthProtoP\x01Zggithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/client_ssl_auth/v2;client_ssl_authv2\xf2\x98\xfe\x8f\x05\x35\x12\x33\x65nvoy.extensions.filters.network.client_ssl_auth.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.client_ssl_auth.v2.client_ssl_auth_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n<io.envoyproxy.envoy.config.filter.network.client_ssl_auth.v2B\022ClientSslAuthProtoP\001Zggithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/client_ssl_auth/v2;client_ssl_authv2\362\230\376\217\0055\0223envoy.extensions.filters.network.client_ssl_auth.v3\272\200\310\321\006\002\020\001'
  _CLIENTSSLAUTH.fields_by_name['auth_api_cluster']._options = None
  _CLIENTSSLAUTH.fields_by_name['auth_api_cluster']._serialized_options = b'\372B\004r\002 \001'
  _CLIENTSSLAUTH.fields_by_name['stat_prefix']._options = None
  _CLIENTSSLAUTH.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _globals['_CLIENTSSLAUTH']._serialized_start=274
  _globals['_CLIENTSSLAUTH']._serialized_end=457
# @@protoc_insertion_point(module_scope)
