# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/transcoder/v2/transcoder.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7envoy/config/filter/http/transcoder/v2/transcoder.proto\x12&envoy.config.filter.http.transcoder.v2\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x9f\x04\n\x12GrpcJsonTranscoder\x12\x1a\n\x10proto_descriptor\x18\x01 \x01(\tH\x00\x12\x1e\n\x14proto_descriptor_bin\x18\x04 \x01(\x0cH\x00\x12\x1a\n\x08services\x18\x02 \x03(\tB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12^\n\rprint_options\x18\x03 \x01(\x0b\x32G.envoy.config.filter.http.transcoder.v2.GrpcJsonTranscoder.PrintOptions\x12$\n\x1cmatch_incoming_request_route\x18\x05 \x01(\x08\x12 \n\x18ignored_query_parameters\x18\x06 \x03(\t\x12\x14\n\x0c\x61uto_mapping\x18\x07 \x01(\x08\x12\'\n\x1fignore_unknown_query_parameters\x18\x08 \x01(\x08\x12\x1b\n\x13\x63onvert_grpc_status\x18\t \x01(\x08\x1a\x95\x01\n\x0cPrintOptions\x12\x16\n\x0e\x61\x64\x64_whitespace\x18\x01 \x01(\x08\x12%\n\x1d\x61lways_print_primitive_fields\x18\x02 \x01(\x08\x12\"\n\x1a\x61lways_print_enums_as_ints\x18\x03 \x01(\x08\x12\"\n\x1apreserve_proto_field_names\x18\x04 \x01(\x08\x42\x15\n\x0e\x64\x65scriptor_set\x12\x03\xf8\x42\x01\x42\xea\x01\n4io.envoyproxy.envoy.config.filter.http.transcoder.v2B\x0fTranscoderProtoP\x01ZZgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/transcoder/v2;transcoderv2\xf2\x98\xfe\x8f\x05\x37\x12\x35\x65nvoy.extensions.filters.http.grpc_json_transcoder.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.transcoder.v2.transcoder_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n4io.envoyproxy.envoy.config.filter.http.transcoder.v2B\017TranscoderProtoP\001ZZgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/transcoder/v2;transcoderv2\362\230\376\217\0057\0225envoy.extensions.filters.http.grpc_json_transcoder.v3\272\200\310\321\006\002\020\001'
  _GRPCJSONTRANSCODER.oneofs_by_name['descriptor_set']._options = None
  _GRPCJSONTRANSCODER.oneofs_by_name['descriptor_set']._serialized_options = b'\370B\001'
  _GRPCJSONTRANSCODER.fields_by_name['services']._options = None
  _GRPCJSONTRANSCODER.fields_by_name['services']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_GRPCJSONTRANSCODER']._serialized_start=188
  _globals['_GRPCJSONTRANSCODER']._serialized_end=731
  _globals['_GRPCJSONTRANSCODER_PRINTOPTIONS']._serialized_start=559
  _globals['_GRPCJSONTRANSCODER_PRINTOPTIONS']._serialized_end=708
# @@protoc_insertion_point(module_scope)
