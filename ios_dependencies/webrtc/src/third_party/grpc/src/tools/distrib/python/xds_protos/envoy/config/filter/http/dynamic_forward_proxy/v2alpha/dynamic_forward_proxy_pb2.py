# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/dynamic_forward_proxy/v2alpha/dynamic_forward_proxy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.common.dynamic_forward_proxy.v2alpha import dns_cache_pb2 as envoy_dot_config_dot_common_dot_dynamic__forward__proxy_dot_v2alpha_dot_dns__cache__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nRenvoy/config/filter/http/dynamic_forward_proxy/v2alpha/dynamic_forward_proxy.proto\x12\x36\x65nvoy.config.filter.http.dynamic_forward_proxy.v2alpha\x1a\x41\x65nvoy/config/common/dynamic_forward_proxy/v2alpha/dns_cache.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"u\n\x0c\x46ilterConfig\x12\x65\n\x10\x64ns_cache_config\x18\x01 \x01(\x0b\x32\x41.envoy.config.common.dynamic_forward_proxy.v2alpha.DnsCacheConfigB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\"\xa1\x01\n\x0ePerRouteConfig\x12\x34\n\x0chost_rewrite\x18\x01 \x01(\tB\x1c\xf2\x98\xfe\x8f\x05\x16\n\x14host_rewrite_literalH\x00\x12?\n\x18\x61uto_host_rewrite_header\x18\x02 \x01(\tB\x1b\xf2\x98\xfe\x8f\x05\x15\n\x13host_rewrite_headerH\x00\x42\x18\n\x16host_rewrite_specifierB\x87\x02\nDio.envoyproxy.envoy.config.filter.http.dynamic_forward_proxy.v2alphaB\x18\x44ynamicForwardProxyProtoP\x01Z]github.com/envoyproxy/go-control-plane/envoy/config/filter/http/dynamic_forward_proxy/v2alpha\xf2\x98\xfe\x8f\x05\x38\x12\x36\x65nvoy.extensions.filters.http.dynamic_forward_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.dynamic_forward_proxy.v2alpha.dynamic_forward_proxy_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\nDio.envoyproxy.envoy.config.filter.http.dynamic_forward_proxy.v2alphaB\030DynamicForwardProxyProtoP\001Z]github.com/envoyproxy/go-control-plane/envoy/config/filter/http/dynamic_forward_proxy/v2alpha\362\230\376\217\0058\0226envoy.extensions.filters.http.dynamic_forward_proxy.v3\272\200\310\321\006\002\020\001'
  _FILTERCONFIG.fields_by_name['dns_cache_config']._options = None
  _FILTERCONFIG.fields_by_name['dns_cache_config']._serialized_options = b'\372B\005\212\001\002\020\001'
  _PERROUTECONFIG.fields_by_name['host_rewrite']._options = None
  _PERROUTECONFIG.fields_by_name['host_rewrite']._serialized_options = b'\362\230\376\217\005\026\n\024host_rewrite_literal'
  _PERROUTECONFIG.fields_by_name['auto_host_rewrite_header']._options = None
  _PERROUTECONFIG.fields_by_name['auto_host_rewrite_header']._serialized_options = b'\362\230\376\217\005\025\n\023host_rewrite_header'
  _globals['_FILTERCONFIG']._serialized_start=297
  _globals['_FILTERCONFIG']._serialized_end=414
  _globals['_PERROUTECONFIG']._serialized_start=417
  _globals['_PERROUTECONFIG']._serialized_end=578
# @@protoc_insertion_point(module_scope)
