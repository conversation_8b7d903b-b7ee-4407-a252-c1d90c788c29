# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/thrift_proxy/v2alpha1/route.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=envoy/config/filter/network/thrift_proxy/v2alpha1/route.proto\x12\x31\x65nvoy.config.filter.network.thrift_proxy.v2alpha1\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a)envoy/api/v2/route/route_components.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"l\n\x12RouteConfiguration\x12\x0c\n\x04name\x18\x01 \x01(\t\x12H\n\x06routes\x18\x02 \x03(\x0b\x32\x38.envoy.config.filter.network.thrift_proxy.v2alpha1.Route\"\xb8\x01\n\x05Route\x12V\n\x05match\x18\x01 \x01(\x0b\x32=.envoy.config.filter.network.thrift_proxy.v2alpha1.RouteMatchB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12W\n\x05route\x18\x02 \x01(\x0b\x32>.envoy.config.filter.network.thrift_proxy.v2alpha1.RouteActionB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\"\x97\x01\n\nRouteMatch\x12\x15\n\x0bmethod_name\x18\x01 \x01(\tH\x00\x12\x16\n\x0cservice_name\x18\x02 \x01(\tH\x00\x12\x0e\n\x06invert\x18\x03 \x01(\x08\x12\x32\n\x07headers\x18\x04 \x03(\x0b\x32!.envoy.api.v2.route.HeaderMatcherB\x16\n\x0fmatch_specifier\x12\x03\xf8\x42\x01\"\xcc\x02\n\x0bRouteAction\x12\x1a\n\x07\x63luster\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01H\x00\x12_\n\x11weighted_clusters\x18\x02 \x01(\x0b\x32\x42.envoy.config.filter.network.thrift_proxy.v2alpha1.WeightedClusterH\x00\x12!\n\x0e\x63luster_header\x18\x06 \x01(\tB\x07\xfa\x42\x04r\x02 \x01H\x00\x12\x33\n\x0emetadata_match\x18\x03 \x01(\x0b\x32\x1b.envoy.api.v2.core.Metadata\x12\x32\n\x0brate_limits\x18\x04 \x03(\x0b\x32\x1d.envoy.api.v2.route.RateLimit\x12\x1a\n\x12strip_service_name\x18\x05 \x01(\x08\x42\x18\n\x11\x63luster_specifier\x12\x03\xf8\x42\x01\"\x94\x02\n\x0fWeightedCluster\x12l\n\x08\x63lusters\x18\x01 \x03(\x0b\x32P.envoy.config.filter.network.thrift_proxy.v2alpha1.WeightedCluster.ClusterWeightB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1a\x92\x01\n\rClusterWeight\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x35\n\x06weight\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02(\x01\x12\x33\n\x0emetadata_match\x18\x03 \x01(\x0b\x32\x1b.envoy.api.v2.core.MetadataB\xe9\x01\n?io.envoyproxy.envoy.config.filter.network.thrift_proxy.v2alpha1B\nRouteProtoP\x01ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/thrift_proxy/v2alpha1\xf2\x98\xfe\x8f\x05\x32\x12\x30\x65nvoy.extensions.filters.network.thrift_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.thrift_proxy.v2alpha1.route_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n?io.envoyproxy.envoy.config.filter.network.thrift_proxy.v2alpha1B\nRouteProtoP\001ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/thrift_proxy/v2alpha1\362\230\376\217\0052\0220envoy.extensions.filters.network.thrift_proxy.v3\272\200\310\321\006\002\020\001'
  _ROUTE.fields_by_name['match']._options = None
  _ROUTE.fields_by_name['match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTE.fields_by_name['route']._options = None
  _ROUTE.fields_by_name['route']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTEMATCH.oneofs_by_name['match_specifier']._options = None
  _ROUTEMATCH.oneofs_by_name['match_specifier']._serialized_options = b'\370B\001'
  _ROUTEACTION.oneofs_by_name['cluster_specifier']._options = None
  _ROUTEACTION.oneofs_by_name['cluster_specifier']._serialized_options = b'\370B\001'
  _ROUTEACTION.fields_by_name['cluster']._options = None
  _ROUTEACTION.fields_by_name['cluster']._serialized_options = b'\372B\004r\002 \001'
  _ROUTEACTION.fields_by_name['cluster_header']._options = None
  _ROUTEACTION.fields_by_name['cluster_header']._serialized_options = b'\372B\004r\002 \001'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['name']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['weight']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['weight']._serialized_options = b'\372B\004*\002(\001'
  _WEIGHTEDCLUSTER.fields_by_name['clusters']._options = None
  _WEIGHTEDCLUSTER.fields_by_name['clusters']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_ROUTECONFIGURATION']._serialized_start=309
  _globals['_ROUTECONFIGURATION']._serialized_end=417
  _globals['_ROUTE']._serialized_start=420
  _globals['_ROUTE']._serialized_end=604
  _globals['_ROUTEMATCH']._serialized_start=607
  _globals['_ROUTEMATCH']._serialized_end=758
  _globals['_ROUTEACTION']._serialized_start=761
  _globals['_ROUTEACTION']._serialized_end=1093
  _globals['_WEIGHTEDCLUSTER']._serialized_start=1096
  _globals['_WEIGHTEDCLUSTER']._serialized_end=1372
  _globals['_WEIGHTEDCLUSTER_CLUSTERWEIGHT']._serialized_start=1226
  _globals['_WEIGHTEDCLUSTER_CLUSTERWEIGHT']._serialized_end=1372
# @@protoc_insertion_point(module_scope)
