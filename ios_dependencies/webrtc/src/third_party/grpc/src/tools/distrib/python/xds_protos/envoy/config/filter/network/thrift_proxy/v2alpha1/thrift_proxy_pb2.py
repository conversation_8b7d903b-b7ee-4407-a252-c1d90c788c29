# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/thrift_proxy/v2alpha1/thrift_proxy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.filter.network.thrift_proxy.v2alpha1 import route_pb2 as envoy_dot_config_dot_filter_dot_network_dot_thrift__proxy_dot_v2alpha1_dot_route__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nDenvoy/config/filter/network/thrift_proxy/v2alpha1/thrift_proxy.proto\x12\x31\x65nvoy.config.filter.network.thrift_proxy.v2alpha1\x1a=envoy/config/filter/network/thrift_proxy/v2alpha1/route.proto\x1a\x19google/protobuf/any.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x9d\x03\n\x0bThriftProxy\x12]\n\ttransport\x18\x02 \x01(\x0e\<EMAIL>.thrift_proxy.v2alpha1.TransportTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12[\n\x08protocol\x18\x03 \x01(\x0e\x32?.envoy.config.filter.network.thrift_proxy.v2alpha1.ProtocolTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12[\n\x0croute_config\x18\x04 \x01(\x0b\x32\x45.envoy.config.filter.network.thrift_proxy.v2alpha1.RouteConfiguration\x12W\n\x0ethrift_filters\x18\x05 \x03(\x0b\x32?.envoy.config.filter.network.thrift_proxy.v2alpha1.ThriftFilter\"\x91\x01\n\x0cThriftFilter\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12-\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x17.google.protobuf.StructB\x02\x18\x01H\x00\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\r\n\x0b\x63onfig_type\"\xd3\x01\n\x15ThriftProtocolOptions\x12]\n\ttransport\x18\x01 \x01(\x0e\<EMAIL>.thrift_proxy.v2alpha1.TransportTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12[\n\x08protocol\x18\x02 \x01(\x0e\x32?.envoy.config.filter.network.thrift_proxy.v2alpha1.ProtocolTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01*I\n\rTransportType\x12\x12\n\x0e\x41UTO_TRANSPORT\x10\x00\x12\n\n\x06\x46RAMED\x10\x01\x12\x0c\n\x08UNFRAMED\x10\x02\x12\n\n\x06HEADER\x10\x03*W\n\x0cProtocolType\x12\x11\n\rAUTO_PROTOCOL\x10\x00\x12\n\n\x06\x42INARY\x10\x01\x12\x0e\n\nLAX_BINARY\x10\x02\x12\x0b\n\x07\x43OMPACT\x10\x03\x12\x0b\n\x07TWITTER\x10\x04\x42\xef\x01\n?io.envoyproxy.envoy.config.filter.network.thrift_proxy.v2alpha1B\x10ThriftProxyProtoP\x01ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/thrift_proxy/v2alpha1\xf2\x98\xfe\x8f\x05\x32\x12\x30\x65nvoy.extensions.filters.network.thrift_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.thrift_proxy.v2alpha1.thrift_proxy_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n?io.envoyproxy.envoy.config.filter.network.thrift_proxy.v2alpha1B\020ThriftProxyProtoP\001ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/thrift_proxy/v2alpha1\362\230\376\217\0052\0220envoy.extensions.filters.network.thrift_proxy.v3\272\200\310\321\006\002\020\001'
  _THRIFTPROXY.fields_by_name['transport']._options = None
  _THRIFTPROXY.fields_by_name['transport']._serialized_options = b'\372B\005\202\001\002\020\001'
  _THRIFTPROXY.fields_by_name['protocol']._options = None
  _THRIFTPROXY.fields_by_name['protocol']._serialized_options = b'\372B\005\202\001\002\020\001'
  _THRIFTPROXY.fields_by_name['stat_prefix']._options = None
  _THRIFTPROXY.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _THRIFTFILTER.fields_by_name['name']._options = None
  _THRIFTFILTER.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _THRIFTFILTER.fields_by_name['config']._options = None
  _THRIFTFILTER.fields_by_name['config']._serialized_options = b'\030\001'
  _THRIFTPROTOCOLOPTIONS.fields_by_name['transport']._options = None
  _THRIFTPROTOCOLOPTIONS.fields_by_name['transport']._serialized_options = b'\372B\005\202\001\002\020\001'
  _THRIFTPROTOCOLOPTIONS.fields_by_name['protocol']._options = None
  _THRIFTPROTOCOLOPTIONS.fields_by_name['protocol']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_TRANSPORTTYPE']._serialized_start=1109
  _globals['_TRANSPORTTYPE']._serialized_end=1182
  _globals['_PROTOCOLTYPE']._serialized_start=1184
  _globals['_PROTOCOLTYPE']._serialized_end=1271
  _globals['_THRIFTPROXY']._serialized_start=332
  _globals['_THRIFTPROXY']._serialized_end=745
  _globals['_THRIFTFILTER']._serialized_start=748
  _globals['_THRIFTFILTER']._serialized_end=893
  _globals['_THRIFTPROTOCOLOPTIONS']._serialized_start=896
  _globals['_THRIFTPROTOCOLOPTIONS']._serialized_end=1107
# @@protoc_insertion_point(module_scope)
