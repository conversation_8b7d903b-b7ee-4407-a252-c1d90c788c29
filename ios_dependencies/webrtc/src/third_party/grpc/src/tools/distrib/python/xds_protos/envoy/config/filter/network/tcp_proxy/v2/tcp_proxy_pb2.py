# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/tcp_proxy/v2/tcp_proxy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import address_pb2 as envoy_dot_api_dot_v2_dot_core_dot_address__pb2
from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.config.filter.accesslog.v2 import accesslog_pb2 as envoy_dot_config_dot_filter_dot_accesslog_dot_v2_dot_accesslog__pb2
from envoy.type import hash_policy_pb2 as envoy_dot_type_dot_hash__policy__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8envoy/config/filter/network/tcp_proxy/v2/tcp_proxy.proto\x12(envoy.config.filter.network.tcp_proxy.v2\x1a\x1f\x65nvoy/api/v2/core/address.proto\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a\x30\x65nvoy/config/filter/accesslog/v2/accesslog.proto\x1a\x1c\x65nvoy/type/hash_policy.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xf1\n\n\x08TcpProxy\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x11\n\x07\x63luster\x18\x02 \x01(\tH\x00\x12_\n\x11weighted_clusters\x18\n \x01(\x0b\x32\x42.envoy.config.filter.network.tcp_proxy.v2.TcpProxy.WeightedClusterH\x00\x12\x33\n\x0emetadata_match\x18\t \x01(\x0b\x32\x1b.envoy.api.v2.core.Metadata\x12/\n\x0cidle_timeout\x18\x08 \x01(\x0b\x32\x19.google.protobuf.Duration\x12:\n\x17\x64ownstream_idle_timeout\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x38\n\x15upstream_idle_timeout\x18\x04 \x01(\x0b\x32\x19.google.protobuf.Duration\x12?\n\naccess_log\x18\x05 \x03(\x0b\x32+.envoy.config.filter.accesslog.v2.AccessLog\x12Z\n\rdeprecated_v1\x18\x06 \x01(\x0b\x32?.envoy.config.filter.network.tcp_proxy.v2.TcpProxy.DeprecatedV1B\x02\x18\x01\x12\x43\n\x14max_connect_attempts\x18\x07 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02(\x01\x12\x35\n\x0bhash_policy\x18\x0b \x03(\x0b\x32\x16.envoy.type.HashPolicyB\x08\xfa\x42\x05\x92\x01\x02\x10\x01\x12\\\n\x10tunneling_config\x18\x0c \x01(\x0b\x32\x42.envoy.config.filter.network.tcp_proxy.v2.TcpProxy.TunnelingConfig\x1a\xbf\x02\n\x0c\x44\x65precatedV1\x12\x62\n\x06routes\x18\x01 \x03(\x0b\x32H.envoy.config.filter.network.tcp_proxy.v2.TcpProxy.DeprecatedV1.TCPRouteB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1a\xc6\x01\n\x08TCPRoute\x12\x18\n\x07\x63luster\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x39\n\x13\x64\x65stination_ip_list\x18\x02 \x03(\x0b\x32\x1c.envoy.api.v2.core.CidrRange\x12\x19\n\x11\x64\x65stination_ports\x18\x03 \x01(\t\x12\x34\n\x0esource_ip_list\x18\x04 \x03(\x0b\x32\x1c.envoy.api.v2.core.CidrRange\x12\x14\n\x0csource_ports\x18\x05 \x01(\t:\x02\x18\x01\x1a\xf5\x01\n\x0fWeightedCluster\x12l\n\x08\x63lusters\x18\x01 \x03(\x0b\x32P.envoy.config.filter.network.tcp_proxy.v2.TcpProxy.WeightedCluster.ClusterWeightB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1at\n\rClusterWeight\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x17\n\x06weight\x18\x02 \x01(\rB\x07\xfa\x42\x04*\x02(\x01\x12\x33\n\x0emetadata_match\x18\x03 \x01(\x0b\x32\x1b.envoy.api.v2.core.Metadata\x1a,\n\x0fTunnelingConfig\x12\x19\n\x08hostname\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x42\x18\n\x11\x63luster_specifier\x12\x03\xf8\x42\x01\x42\xe3\x01\n6io.envoyproxy.envoy.config.filter.network.tcp_proxy.v2B\rTcpProxyProtoP\x01Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/tcp_proxy/v2;tcp_proxyv2\xf2\x98\xfe\x8f\x05/\x12-envoy.extensions.filters.network.tcp_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.tcp_proxy.v2.tcp_proxy_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n6io.envoyproxy.envoy.config.filter.network.tcp_proxy.v2B\rTcpProxyProtoP\001Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/tcp_proxy/v2;tcp_proxyv2\362\230\376\217\005/\022-envoy.extensions.filters.network.tcp_proxy.v3\272\200\310\321\006\002\020\001'
  _TCPPROXY_DEPRECATEDV1_TCPROUTE.fields_by_name['cluster']._options = None
  _TCPPROXY_DEPRECATEDV1_TCPROUTE.fields_by_name['cluster']._serialized_options = b'\372B\004r\002 \001'
  _TCPPROXY_DEPRECATEDV1.fields_by_name['routes']._options = None
  _TCPPROXY_DEPRECATEDV1.fields_by_name['routes']._serialized_options = b'\372B\005\222\001\002\010\001'
  _TCPPROXY_DEPRECATEDV1._options = None
  _TCPPROXY_DEPRECATEDV1._serialized_options = b'\030\001'
  _TCPPROXY_WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['name']._options = None
  _TCPPROXY_WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _TCPPROXY_WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['weight']._options = None
  _TCPPROXY_WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['weight']._serialized_options = b'\372B\004*\002(\001'
  _TCPPROXY_WEIGHTEDCLUSTER.fields_by_name['clusters']._options = None
  _TCPPROXY_WEIGHTEDCLUSTER.fields_by_name['clusters']._serialized_options = b'\372B\005\222\001\002\010\001'
  _TCPPROXY_TUNNELINGCONFIG.fields_by_name['hostname']._options = None
  _TCPPROXY_TUNNELINGCONFIG.fields_by_name['hostname']._serialized_options = b'\372B\004r\002 \001'
  _TCPPROXY.oneofs_by_name['cluster_specifier']._options = None
  _TCPPROXY.oneofs_by_name['cluster_specifier']._serialized_options = b'\370B\001'
  _TCPPROXY.fields_by_name['stat_prefix']._options = None
  _TCPPROXY.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _TCPPROXY.fields_by_name['deprecated_v1']._options = None
  _TCPPROXY.fields_by_name['deprecated_v1']._serialized_options = b'\030\001'
  _TCPPROXY.fields_by_name['max_connect_attempts']._options = None
  _TCPPROXY.fields_by_name['max_connect_attempts']._serialized_options = b'\372B\004*\002(\001'
  _TCPPROXY.fields_by_name['hash_policy']._options = None
  _TCPPROXY.fields_by_name['hash_policy']._serialized_options = b'\372B\005\222\001\002\020\001'
  _globals['_TCPPROXY']._serialized_start=398
  _globals['_TCPPROXY']._serialized_end=1791
  _globals['_TCPPROXY_DEPRECATEDV1']._serialized_start=1152
  _globals['_TCPPROXY_DEPRECATEDV1']._serialized_end=1471
  _globals['_TCPPROXY_DEPRECATEDV1_TCPROUTE']._serialized_start=1269
  _globals['_TCPPROXY_DEPRECATEDV1_TCPROUTE']._serialized_end=1467
  _globals['_TCPPROXY_WEIGHTEDCLUSTER']._serialized_start=1474
  _globals['_TCPPROXY_WEIGHTEDCLUSTER']._serialized_end=1719
  _globals['_TCPPROXY_WEIGHTEDCLUSTER_CLUSTERWEIGHT']._serialized_start=1603
  _globals['_TCPPROXY_WEIGHTEDCLUSTER_CLUSTERWEIGHT']._serialized_end=1719
  _globals['_TCPPROXY_TUNNELINGCONFIG']._serialized_start=1721
  _globals['_TCPPROXY_TUNNELINGCONFIG']._serialized_end=1765
# @@protoc_insertion_point(module_scope)
