# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/ext_authz/v2/ext_authz.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import grpc_service_pb2 as envoy_dot_api_dot_v2_dot_core_dot_grpc__service__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8envoy/config/filter/network/ext_authz/v2/ext_authz.proto\x12(envoy.config.filter.network.ext_authz.v2\x1a$envoy/api/v2/core/grpc_service.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x9c\x01\n\x08\x45xtAuthz\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x34\n\x0cgrpc_service\x18\x02 \x01(\x0b\x32\x1e.envoy.api.v2.core.GrpcService\x12\x1a\n\x12\x66\x61ilure_mode_allow\x18\x03 \x01(\x08\x12 \n\x18include_peer_certificate\x18\x04 \x01(\x08\x42\xe3\x01\n6io.envoyproxy.envoy.config.filter.network.ext_authz.v2B\rExtAuthzProtoP\x01Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/ext_authz/v2;ext_authzv2\xf2\x98\xfe\x8f\x05/\x12-envoy.extensions.filters.network.ext_authz.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.ext_authz.v2.ext_authz_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n6io.envoyproxy.envoy.config.filter.network.ext_authz.v2B\rExtAuthzProtoP\001Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/ext_authz/v2;ext_authzv2\362\230\376\217\005/\022-envoy.extensions.filters.network.ext_authz.v3\272\200\310\321\006\002\020\001'
  _EXTAUTHZ.fields_by_name['stat_prefix']._options = None
  _EXTAUTHZ.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _globals['_EXTAUTHZ']._serialized_start=229
  _globals['_EXTAUTHZ']._serialized_end=385
# @@protoc_insertion_point(module_scope)
