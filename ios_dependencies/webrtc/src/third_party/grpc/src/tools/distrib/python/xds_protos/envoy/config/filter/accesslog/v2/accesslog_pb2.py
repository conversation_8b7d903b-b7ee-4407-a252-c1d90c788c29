# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/accesslog/v2/accesslog.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from envoy.type import percent_pb2 as envoy_dot_type_dot_percent__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0envoy/config/filter/accesslog/v2/accesslog.proto\x12 envoy.config.filter.accesslog.v2\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a)envoy/api/v2/route/route_components.proto\x1a\x18\x65nvoy/type/percent.proto\x1a\x19google/protobuf/any.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xc8\x01\n\tAccessLog\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x41\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x31.envoy.config.filter.accesslog.v2.AccessLogFilter\x12-\n\x06\x63onfig\x18\x03 \x01(\x0b\x32\x17.google.protobuf.StructB\x02\x18\x01H\x00\x12,\n\x0ctyped_config\x18\x04 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\r\n\x0b\x63onfig_type\"\x82\x07\n\x0f\x41\x63\x63\x65ssLogFilter\x12P\n\x12status_code_filter\x18\x01 \x01(\x0b\x32\x32.envoy.config.filter.accesslog.v2.StatusCodeFilterH\x00\x12K\n\x0f\x64uration_filter\x18\x02 \x01(\x0b\x32\x30.envoy.config.filter.accesslog.v2.DurationFilterH\x00\x12Y\n\x17not_health_check_filter\x18\x03 \x01(\x0b\x32\x36.envoy.config.filter.accesslog.v2.NotHealthCheckFilterH\x00\x12M\n\x10traceable_filter\x18\x04 \x01(\x0b\x32\x31.envoy.config.filter.accesslog.v2.TraceableFilterH\x00\x12I\n\x0eruntime_filter\x18\x05 \x01(\x0b\x32/.envoy.config.filter.accesslog.v2.RuntimeFilterH\x00\x12\x41\n\nand_filter\x18\x06 \x01(\x0b\x32+.envoy.config.filter.accesslog.v2.AndFilterH\x00\x12?\n\tor_filter\x18\x07 \x01(\x0b\x32*.envoy.config.filter.accesslog.v2.OrFilterH\x00\x12G\n\rheader_filter\x18\x08 \x01(\x0b\x32..envoy.config.filter.accesslog.v2.HeaderFilterH\x00\x12T\n\x14response_flag_filter\x18\t \x01(\x0b\x32\x34.envoy.config.filter.accesslog.v2.ResponseFlagFilterH\x00\x12P\n\x12grpc_status_filter\x18\n \x01(\x0b\x32\x32.envoy.config.filter.accesslog.v2.GrpcStatusFilterH\x00\x12M\n\x10\x65xtension_filter\x18\x0b \x01(\x0b\x32\x31.envoy.config.filter.accesslog.v2.ExtensionFilterH\x00\x42\x17\n\x10\x66ilter_specifier\x12\x03\xf8\x42\x01\"\xae\x01\n\x10\x43omparisonFilter\x12K\n\x02op\x18\x01 \x01(\x0e\x32\x35.envoy.config.filter.accesslog.v2.ComparisonFilter.OpB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .envoy.api.v2.core.RuntimeUInt32\"\x1c\n\x02Op\x12\x06\n\x02\x45Q\x10\x00\x12\x06\n\x02GE\x10\x01\x12\x06\n\x02LE\x10\x02\"d\n\x10StatusCodeFilter\x12P\n\ncomparison\x18\x01 \x01(\x0b\x32\x32.envoy.config.filter.accesslog.v2.ComparisonFilterB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\"b\n\x0e\x44urationFilter\x12P\n\ncomparison\x18\x01 \x01(\x0b\x32\x32.envoy.config.filter.accesslog.v2.ComparisonFilterB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\"\x16\n\x14NotHealthCheckFilter\"\x11\n\x0fTraceableFilter\"\x89\x01\n\rRuntimeFilter\x12\x1c\n\x0bruntime_key\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x36\n\x0fpercent_sampled\x18\x02 \x01(\x0b\x32\x1d.envoy.type.FractionalPercent\x12\"\n\x1ause_independent_randomness\x18\x03 \x01(\x08\"Y\n\tAndFilter\x12L\n\x07\x66ilters\x18\x01 \x03(\x0b\x32\x31.envoy.config.filter.accesslog.v2.AccessLogFilterB\x08\xfa\x42\x05\x92\x01\x02\x08\x02\"X\n\x08OrFilter\x12L\n\x07\x66ilters\x18\x02 \x03(\x0b\x32\x31.envoy.config.filter.accesslog.v2.AccessLogFilterB\x08\xfa\x42\x05\x92\x01\x02\x08\x02\"K\n\x0cHeaderFilter\x12;\n\x06header\x18\x01 \x01(\x0b\x32!.envoy.api.v2.route.HeaderMatcherB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\"\x81\x01\n\x12ResponseFlagFilter\x12k\n\x05\x66lags\x18\x01 \x03(\tB\\\xfa\x42Y\x92\x01V\"TrRR\x02LHR\x02UHR\x02UTR\x02LRR\x02URR\x02UFR\x02UCR\x02UOR\x02NRR\x02\x44IR\x02\x46IR\x02RLR\x04UAEXR\x04RLSER\x02\x44\x43R\x03URXR\x02SIR\x02IHR\x03\x44PE\"\xba\x03\n\x10GrpcStatusFilter\x12Z\n\x08statuses\x18\x01 \x03(\x0e\x32\x39.envoy.config.filter.accesslog.v2.GrpcStatusFilter.StatusB\r\xfa\x42\n\x92\x01\x07\"\x05\x82\x01\x02\x10\x01\x12\x0f\n\x07\x65xclude\x18\x02 \x01(\x08\"\xb8\x02\n\x06Status\x12\x06\n\x02OK\x10\x00\x12\x0c\n\x08\x43\x41NCELED\x10\x01\x12\x0b\n\x07UNKNOWN\x10\x02\x12\x14\n\x10INVALID_ARGUMENT\x10\x03\x12\x15\n\x11\x44\x45\x41\x44LINE_EXCEEDED\x10\x04\x12\r\n\tNOT_FOUND\x10\x05\x12\x12\n\x0e\x41LREADY_EXISTS\x10\x06\x12\x15\n\x11PERMISSION_DENIED\x10\x07\x12\x16\n\x12RESOURCE_EXHAUSTED\x10\x08\x12\x17\n\x13\x46\x41ILED_PRECONDITION\x10\t\x12\x0b\n\x07\x41\x42ORTED\x10\n\x12\x10\n\x0cOUT_OF_RANGE\x10\x0b\x12\x11\n\rUNIMPLEMENTED\x10\x0c\x12\x0c\n\x08INTERNAL\x10\r\x12\x0f\n\x0bUNAVAILABLE\x10\x0e\x12\r\n\tDATA_LOSS\x10\x0f\x12\x13\n\x0fUNAUTHENTICATED\x10\x10\"\x8b\x01\n\x0f\x45xtensionFilter\x12\x0c\n\x04name\x18\x01 \x01(\t\x12-\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x17.google.protobuf.StructB\x02\x18\x01H\x00\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\r\n\x0b\x63onfig_typeB\xc0\x01\n.io.envoyproxy.envoy.config.filter.accesslog.v2B\x0e\x41\x63\x63\x65sslogProtoP\x01ZSgithub.com/envoyproxy/go-control-plane/envoy/config/filter/accesslog/v2;accesslogv2\xf2\x98\xfe\x8f\x05\x1b\x12\x19\x65nvoy.config.accesslog.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.accesslog.v2.accesslog_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n.io.envoyproxy.envoy.config.filter.accesslog.v2B\016AccesslogProtoP\001ZSgithub.com/envoyproxy/go-control-plane/envoy/config/filter/accesslog/v2;accesslogv2\362\230\376\217\005\033\022\031envoy.config.accesslog.v3\272\200\310\321\006\002\020\001'
  _ACCESSLOG.fields_by_name['config']._options = None
  _ACCESSLOG.fields_by_name['config']._serialized_options = b'\030\001'
  _ACCESSLOGFILTER.oneofs_by_name['filter_specifier']._options = None
  _ACCESSLOGFILTER.oneofs_by_name['filter_specifier']._serialized_options = b'\370B\001'
  _COMPARISONFILTER.fields_by_name['op']._options = None
  _COMPARISONFILTER.fields_by_name['op']._serialized_options = b'\372B\005\202\001\002\020\001'
  _STATUSCODEFILTER.fields_by_name['comparison']._options = None
  _STATUSCODEFILTER.fields_by_name['comparison']._serialized_options = b'\372B\005\212\001\002\020\001'
  _DURATIONFILTER.fields_by_name['comparison']._options = None
  _DURATIONFILTER.fields_by_name['comparison']._serialized_options = b'\372B\005\212\001\002\020\001'
  _RUNTIMEFILTER.fields_by_name['runtime_key']._options = None
  _RUNTIMEFILTER.fields_by_name['runtime_key']._serialized_options = b'\372B\004r\002 \001'
  _ANDFILTER.fields_by_name['filters']._options = None
  _ANDFILTER.fields_by_name['filters']._serialized_options = b'\372B\005\222\001\002\010\002'
  _ORFILTER.fields_by_name['filters']._options = None
  _ORFILTER.fields_by_name['filters']._serialized_options = b'\372B\005\222\001\002\010\002'
  _HEADERFILTER.fields_by_name['header']._options = None
  _HEADERFILTER.fields_by_name['header']._serialized_options = b'\372B\005\212\001\002\020\001'
  _RESPONSEFLAGFILTER.fields_by_name['flags']._options = None
  _RESPONSEFLAGFILTER.fields_by_name['flags']._serialized_options = b'\372BY\222\001V\"TrRR\002LHR\002UHR\002UTR\002LRR\002URR\002UFR\002UCR\002UOR\002NRR\002DIR\002FIR\002RLR\004UAEXR\004RLSER\002DCR\003URXR\002SIR\002IHR\003DPE'
  _GRPCSTATUSFILTER.fields_by_name['statuses']._options = None
  _GRPCSTATUSFILTER.fields_by_name['statuses']._serialized_options = b'\372B\n\222\001\007\"\005\202\001\002\020\001'
  _EXTENSIONFILTER.fields_by_name['config']._options = None
  _EXTENSIONFILTER.fields_by_name['config']._serialized_options = b'\030\001'
  _globals['_ACCESSLOG']._serialized_start=331
  _globals['_ACCESSLOG']._serialized_end=531
  _globals['_ACCESSLOGFILTER']._serialized_start=534
  _globals['_ACCESSLOGFILTER']._serialized_end=1432
  _globals['_COMPARISONFILTER']._serialized_start=1435
  _globals['_COMPARISONFILTER']._serialized_end=1609
  _globals['_COMPARISONFILTER_OP']._serialized_start=1581
  _globals['_COMPARISONFILTER_OP']._serialized_end=1609
  _globals['_STATUSCODEFILTER']._serialized_start=1611
  _globals['_STATUSCODEFILTER']._serialized_end=1711
  _globals['_DURATIONFILTER']._serialized_start=1713
  _globals['_DURATIONFILTER']._serialized_end=1811
  _globals['_NOTHEALTHCHECKFILTER']._serialized_start=1813
  _globals['_NOTHEALTHCHECKFILTER']._serialized_end=1835
  _globals['_TRACEABLEFILTER']._serialized_start=1837
  _globals['_TRACEABLEFILTER']._serialized_end=1854
  _globals['_RUNTIMEFILTER']._serialized_start=1857
  _globals['_RUNTIMEFILTER']._serialized_end=1994
  _globals['_ANDFILTER']._serialized_start=1996
  _globals['_ANDFILTER']._serialized_end=2085
  _globals['_ORFILTER']._serialized_start=2087
  _globals['_ORFILTER']._serialized_end=2175
  _globals['_HEADERFILTER']._serialized_start=2177
  _globals['_HEADERFILTER']._serialized_end=2252
  _globals['_RESPONSEFLAGFILTER']._serialized_start=2255
  _globals['_RESPONSEFLAGFILTER']._serialized_end=2384
  _globals['_GRPCSTATUSFILTER']._serialized_start=2387
  _globals['_GRPCSTATUSFILTER']._serialized_end=2829
  _globals['_GRPCSTATUSFILTER_STATUS']._serialized_start=2517
  _globals['_GRPCSTATUSFILTER_STATUS']._serialized_end=2829
  _globals['_EXTENSIONFILTER']._serialized_start=2832
  _globals['_EXTENSIONFILTER']._serialized_end=2971
# @@protoc_insertion_point(module_scope)
