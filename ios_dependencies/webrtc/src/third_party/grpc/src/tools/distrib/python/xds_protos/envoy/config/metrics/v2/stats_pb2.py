# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/metrics/v2/stats.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import address_pb2 as envoy_dot_api_dot_v2_dot_core_dot_address__pb2
from envoy.type.matcher import string_pb2 as envoy_dot_type_dot_matcher_dot_string__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#envoy/config/metrics/v2/stats.proto\x12\x17\x65nvoy.config.metrics.v2\x1a\x1f\x65nvoy/api/v2/core/address.proto\x1a\x1f\x65nvoy/type/matcher/string.proto\x1a\x19google/protobuf/any.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x85\x01\n\tStatsSink\x12\x0c\n\x04name\x18\x01 \x01(\t\x12-\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x17.google.protobuf.StructB\x02\x18\x01H\x00\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\r\n\x0b\x63onfig_type\"\xc0\x01\n\x0bStatsConfig\x12\x39\n\nstats_tags\x18\x01 \x03(\x0b\x32%.envoy.config.metrics.v2.TagSpecifier\x12\x38\n\x14use_all_default_tags\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12<\n\rstats_matcher\x18\x03 \x01(\x0b\x32%.envoy.config.metrics.v2.StatsMatcher\"\xbc\x01\n\x0cStatsMatcher\x12\x14\n\nreject_all\x18\x01 \x01(\x08H\x00\x12?\n\x0e\x65xclusion_list\x18\x02 \x01(\x0b\x32%.envoy.type.matcher.ListStringMatcherH\x00\x12?\n\x0einclusion_list\x18\x03 \x01(\x0b\x32%.envoy.type.matcher.ListStringMatcherH\x00\x42\x14\n\rstats_matcher\x12\x03\xf8\x42\x01\"_\n\x0cTagSpecifier\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x19\n\x05regex\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03(\x80\x08H\x00\x12\x15\n\x0b\x66ixed_value\x18\x03 \x01(\tH\x00\x42\x0b\n\ttag_value\"\x80\x01\n\nStatsdSink\x12-\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32\x1a.envoy.api.v2.core.AddressH\x00\x12\x1a\n\x10tcp_cluster_name\x18\x02 \x01(\tH\x00\x12\x0e\n\x06prefix\x18\x03 \x01(\tB\x17\n\x10statsd_specifier\x12\x03\xf8\x42\x01\"q\n\rDogStatsdSink\x12-\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32\x1a.envoy.api.v2.core.AddressH\x00\x12\x0e\n\x06prefix\x18\x03 \x01(\tB\x1b\n\x14\x64og_statsd_specifier\x12\x03\xf8\x42\x01J\x04\x08\x02\x10\x03\"\"\n\x0bHystrixSink\x12\x13\n\x0bnum_buckets\x18\x01 \x01(\x03\x42\x87\x01\n%io.envoyproxy.envoy.config.metrics.v2B\nStatsProtoP\x01ZHgithub.com/envoyproxy/go-control-plane/envoy/config/metrics/v2;metricsv2\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.metrics.v2.stats_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n%io.envoyproxy.envoy.config.metrics.v2B\nStatsProtoP\001ZHgithub.com/envoyproxy/go-control-plane/envoy/config/metrics/v2;metricsv2\272\200\310\321\006\002\020\001'
  _STATSSINK.fields_by_name['config']._options = None
  _STATSSINK.fields_by_name['config']._serialized_options = b'\030\001'
  _STATSMATCHER.oneofs_by_name['stats_matcher']._options = None
  _STATSMATCHER.oneofs_by_name['stats_matcher']._serialized_options = b'\370B\001'
  _TAGSPECIFIER.fields_by_name['regex']._options = None
  _TAGSPECIFIER.fields_by_name['regex']._serialized_options = b'\372B\005r\003(\200\010'
  _STATSDSINK.oneofs_by_name['statsd_specifier']._options = None
  _STATSDSINK.oneofs_by_name['statsd_specifier']._serialized_options = b'\370B\001'
  _DOGSTATSDSINK.oneofs_by_name['dog_statsd_specifier']._options = None
  _DOGSTATSDSINK.oneofs_by_name['dog_statsd_specifier']._serialized_options = b'\370B\001'
  _globals['_STATSSINK']._serialized_start=276
  _globals['_STATSSINK']._serialized_end=409
  _globals['_STATSCONFIG']._serialized_start=412
  _globals['_STATSCONFIG']._serialized_end=604
  _globals['_STATSMATCHER']._serialized_start=607
  _globals['_STATSMATCHER']._serialized_end=795
  _globals['_TAGSPECIFIER']._serialized_start=797
  _globals['_TAGSPECIFIER']._serialized_end=892
  _globals['_STATSDSINK']._serialized_start=895
  _globals['_STATSDSINK']._serialized_end=1023
  _globals['_DOGSTATSDSINK']._serialized_start=1025
  _globals['_DOGSTATSDSINK']._serialized_end=1138
  _globals['_HYSTRIXSINK']._serialized_start=1140
  _globals['_HYSTRIXSINK']._serialized_end=1174
# @@protoc_insertion_point(module_scope)
