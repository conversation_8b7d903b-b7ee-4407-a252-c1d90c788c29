# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/jwt_authn/v2alpha/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.api.v2.core import http_uri_pb2 as envoy_dot_api_dot_v2_dot_core_dot_http__uri__pb2
from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7envoy/config/filter/http/jwt_authn/v2alpha/config.proto\x12*envoy.config.filter.http.jwt_authn.v2alpha\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a envoy/api/v2/core/http_uri.proto\x1a)envoy/api/v2/route/route_components.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x8b\x03\n\x0bJwtProvider\x12\x17\n\x06issuer\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x11\n\taudiences\x18\x02 \x03(\t\x12M\n\x0bremote_jwks\x18\x03 \x01(\x0b\x32\x36.envoy.config.filter.http.jwt_authn.v2alpha.RemoteJwksH\x00\x12\x33\n\nlocal_jwks\x18\x04 \x01(\x0b\x32\x1d.envoy.api.v2.core.DataSourceH\x00\x12\x0f\n\x07\x66orward\x18\x05 \x01(\x08\x12K\n\x0c\x66rom_headers\x18\x06 \x03(\x0b\x32\x35.envoy.config.filter.http.jwt_authn.v2alpha.JwtHeader\x12\x13\n\x0b\x66rom_params\x18\x07 \x03(\t\x12\x1e\n\x16\x66orward_payload_header\x18\x08 \x01(\t\x12\x1b\n\x13payload_in_metadata\x18\t \x01(\tB\x1c\n\x15jwks_source_specifier\x12\x03\xf8\x42\x01\"m\n\nRemoteJwks\x12,\n\x08http_uri\x18\x01 \x01(\x0b\x32\x1a.envoy.api.v2.core.HttpUri\x12\x31\n\x0e\x63\x61\x63he_duration\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration\"8\n\tJwtHeader\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x14\n\x0cvalue_prefix\x18\x02 \x01(\t\"A\n\x15ProviderWithAudiences\x12\x15\n\rprovider_name\x18\x01 \x01(\t\x12\x11\n\taudiences\x18\x02 \x03(\t\"\xc0\x03\n\x0eJwtRequirement\x12\x17\n\rprovider_name\x18\x01 \x01(\tH\x00\x12\x63\n\x16provider_and_audiences\x18\x02 \x01(\x0b\x32\x41.envoy.config.filter.http.jwt_authn.v2alpha.ProviderWithAudiencesH\x00\x12X\n\x0crequires_any\x18\x03 \x01(\x0b\<EMAIL>.jwt_authn.v2alpha.JwtRequirementOrListH\x00\x12Y\n\x0crequires_all\x18\x04 \x01(\x0b\x32\x41.envoy.config.filter.http.jwt_authn.v2alpha.JwtRequirementAndListH\x00\x12\x39\n\x17\x61llow_missing_or_failed\x18\x05 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x12/\n\rallow_missing\x18\x06 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x42\x0f\n\rrequires_type\"r\n\x14JwtRequirementOrList\x12Z\n\x0crequirements\x18\x01 \x03(\x0b\x32:.envoy.config.filter.http.jwt_authn.v2alpha.JwtRequirementB\x08\xfa\x42\x05\x92\x01\x02\x08\x02\"s\n\x15JwtRequirementAndList\x12Z\n\x0crequirements\x18\x01 \x03(\x0b\x32:.envoy.config.filter.http.jwt_authn.v2alpha.JwtRequirementB\x08\xfa\x42\x05\x92\x01\x02\x08\x02\"\x98\x01\n\x0fRequirementRule\x12\x37\n\x05match\x18\x01 \x01(\x0b\x32\x1e.envoy.api.v2.route.RouteMatchB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12L\n\x08requires\x18\x02 \x01(\x0b\x32:.envoy.config.filter.http.jwt_authn.v2alpha.JwtRequirement\"\xf2\x01\n\x0f\x46ilterStateRule\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12[\n\x08requires\x18\x03 \x03(\x0b\x32I.envoy.config.filter.http.jwt_authn.v2alpha.FilterStateRule.RequiresEntry\x1ak\n\rRequiresEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12I\n\x05value\x18\x02 \x01(\x0b\x32:.envoy.config.filter.http.jwt_authn.v2alpha.JwtRequirement:\x02\x38\x01\"\xa3\x03\n\x11JwtAuthentication\x12_\n\tproviders\x18\x01 \x03(\x0b\x32L.envoy.config.filter.http.jwt_authn.v2alpha.JwtAuthentication.ProvidersEntry\x12J\n\x05rules\x18\x02 \x03(\x0b\x32;.envoy.config.filter.http.jwt_authn.v2alpha.RequirementRule\x12W\n\x12\x66ilter_state_rules\x18\x03 \x01(\x0b\x32;.envoy.config.filter.http.jwt_authn.v2alpha.FilterStateRule\x12\x1d\n\x15\x62ypass_cors_preflight\x18\x04 \x01(\x08\x1ai\n\x0eProvidersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.envoy.config.filter.http.jwt_authn.v2alpha.JwtProvider:\x02\x38\x01\x42\xd6\x01\n8io.envoyproxy.envoy.config.filter.http.jwt_authn.v2alphaB\x0b\x43onfigProtoP\x01ZQgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/jwt_authn/v2alpha\xf2\x98\xfe\x8f\x05,\x12*envoy.extensions.filters.http.jwt_authn.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.jwt_authn.v2alpha.config_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n8io.envoyproxy.envoy.config.filter.http.jwt_authn.v2alphaB\013ConfigProtoP\001ZQgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/jwt_authn/v2alpha\362\230\376\217\005,\022*envoy.extensions.filters.http.jwt_authn.v3\272\200\310\321\006\002\020\001'
  _JWTPROVIDER.oneofs_by_name['jwks_source_specifier']._options = None
  _JWTPROVIDER.oneofs_by_name['jwks_source_specifier']._serialized_options = b'\370B\001'
  _JWTPROVIDER.fields_by_name['issuer']._options = None
  _JWTPROVIDER.fields_by_name['issuer']._serialized_options = b'\372B\004r\002 \001'
  _JWTHEADER.fields_by_name['name']._options = None
  _JWTHEADER.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _JWTREQUIREMENTORLIST.fields_by_name['requirements']._options = None
  _JWTREQUIREMENTORLIST.fields_by_name['requirements']._serialized_options = b'\372B\005\222\001\002\010\002'
  _JWTREQUIREMENTANDLIST.fields_by_name['requirements']._options = None
  _JWTREQUIREMENTANDLIST.fields_by_name['requirements']._serialized_options = b'\372B\005\222\001\002\010\002'
  _REQUIREMENTRULE.fields_by_name['match']._options = None
  _REQUIREMENTRULE.fields_by_name['match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _FILTERSTATERULE_REQUIRESENTRY._options = None
  _FILTERSTATERULE_REQUIRESENTRY._serialized_options = b'8\001'
  _FILTERSTATERULE.fields_by_name['name']._options = None
  _FILTERSTATERULE.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _JWTAUTHENTICATION_PROVIDERSENTRY._options = None
  _JWTAUTHENTICATION_PROVIDERSENTRY._serialized_options = b'8\001'
  _globals['_JWTPROVIDER']._serialized_start=360
  _globals['_JWTPROVIDER']._serialized_end=755
  _globals['_REMOTEJWKS']._serialized_start=757
  _globals['_REMOTEJWKS']._serialized_end=866
  _globals['_JWTHEADER']._serialized_start=868
  _globals['_JWTHEADER']._serialized_end=924
  _globals['_PROVIDERWITHAUDIENCES']._serialized_start=926
  _globals['_PROVIDERWITHAUDIENCES']._serialized_end=991
  _globals['_JWTREQUIREMENT']._serialized_start=994
  _globals['_JWTREQUIREMENT']._serialized_end=1442
  _globals['_JWTREQUIREMENTORLIST']._serialized_start=1444
  _globals['_JWTREQUIREMENTORLIST']._serialized_end=1558
  _globals['_JWTREQUIREMENTANDLIST']._serialized_start=1560
  _globals['_JWTREQUIREMENTANDLIST']._serialized_end=1675
  _globals['_REQUIREMENTRULE']._serialized_start=1678
  _globals['_REQUIREMENTRULE']._serialized_end=1830
  _globals['_FILTERSTATERULE']._serialized_start=1833
  _globals['_FILTERSTATERULE']._serialized_end=2075
  _globals['_FILTERSTATERULE_REQUIRESENTRY']._serialized_start=1968
  _globals['_FILTERSTATERULE_REQUIRESENTRY']._serialized_end=2075
  _globals['_JWTAUTHENTICATION']._serialized_start=2078
  _globals['_JWTAUTHENTICATION']._serialized_end=2497
  _globals['_JWTAUTHENTICATION_PROVIDERSENTRY']._serialized_start=2392
  _globals['_JWTAUTHENTICATION_PROVIDERSENTRY']._serialized_end=2497
# @@protoc_insertion_point(module_scope)
