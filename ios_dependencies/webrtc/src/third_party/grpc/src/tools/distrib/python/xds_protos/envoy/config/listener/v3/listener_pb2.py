# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/listener/v3/listener.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.accesslog.v3 import accesslog_pb2 as envoy_dot_config_dot_accesslog_dot_v3_dot_accesslog__pb2
from envoy.config.core.v3 import address_pb2 as envoy_dot_config_dot_core_dot_v3_dot_address__pb2
from envoy.config.core.v3 import base_pb2 as envoy_dot_config_dot_core_dot_v3_dot_base__pb2
from envoy.config.core.v3 import extension_pb2 as envoy_dot_config_dot_core_dot_v3_dot_extension__pb2
from envoy.config.core.v3 import socket_option_pb2 as envoy_dot_config_dot_core_dot_v3_dot_socket__option__pb2
from envoy.config.listener.v3 import api_listener_pb2 as envoy_dot_config_dot_listener_dot_v3_dot_api__listener__pb2
from envoy.config.listener.v3 import listener_components_pb2 as envoy_dot_config_dot_listener_dot_v3_dot_listener__components__pb2
from envoy.config.listener.v3 import udp_listener_config_pb2 as envoy_dot_config_dot_listener_dot_v3_dot_udp__listener__config__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from xds.annotations.v3 import status_pb2 as xds_dot_annotations_dot_v3_dot_status__pb2
from xds.core.v3 import collection_entry_pb2 as xds_dot_core_dot_v3_dot_collection__entry__pb2
from xds.type.matcher.v3 import matcher_pb2 as xds_dot_type_dot_matcher_dot_v3_dot_matcher__pb2
from envoy.annotations import deprecation_pb2 as envoy_dot_annotations_dot_deprecation__pb2
from udpa.annotations import security_pb2 as udpa_dot_annotations_dot_security__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'envoy/config/listener/v3/listener.proto\x12\x18\x65nvoy.config.listener.v3\x1a)envoy/config/accesslog/v3/accesslog.proto\x1a\"envoy/config/core/v3/address.proto\x1a\x1f\x65nvoy/config/core/v3/base.proto\x1a$envoy/config/core/v3/extension.proto\x1a(envoy/config/core/v3/socket_option.proto\x1a+envoy/config/listener/v3/api_listener.proto\x1a\x32\x65nvoy/config/listener/v3/listener_components.proto\x1a\x32\x65nvoy/config/listener/v3/udp_listener_config.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1fxds/annotations/v3/status.proto\x1a\"xds/core/v3/collection_entry.proto\x1a!xds/type/matcher/v3/matcher.proto\x1a#envoy/annotations/deprecation.proto\x1a\x1fudpa/annotations/security.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\x88\x01\n\x11\x41\x64\x64itionalAddress\x12.\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32\x1d.envoy.config.core.v3.Address\x12\x43\n\x0esocket_options\x18\x02 \x01(\x0b\x32+.envoy.config.core.v3.SocketOptionsOverride\"C\n\x12ListenerCollection\x12-\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x1c.xds.core.v3.CollectionEntry\"\xd3\x13\n\x08Listener\x12\x0c\n\x04name\x18\x01 \x01(\t\x12.\n\x07\x61\x64\x64ress\x18\x02 \x01(\x0b\x32\x1d.envoy.config.core.v3.Address\x12I\n\x14\x61\x64\x64itional_addresses\x18! \x03(\x0b\x32+.envoy.config.listener.v3.AdditionalAddress\x12\x13\n\x0bstat_prefix\x18\x1c \x01(\t\x12<\n\rfilter_chains\x18\x03 \x03(\x0b\x32%.envoy.config.listener.v3.FilterChain\x12\x44\n\x14\x66ilter_chain_matcher\x18  \x01(\x0b\x32\x1c.xds.type.matcher.v3.MatcherB\x08\xd2\xc6\xa4\xe1\x06\x02\x08\x01\x12\x34\n\x10use_original_dst\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12\x43\n\x14\x64\x65\x66\x61ult_filter_chain\x18\x19 \x01(\x0b\x32%.envoy.config.listener.v3.FilterChain\x12P\n!per_connection_buffer_limit_bytes\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\x8a\x93\xb7*\x02\x08\x01\x12\x30\n\x08metadata\x18\x06 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x12S\n\rdeprecated_v1\x18\x07 \x01(\x0b\x32/.envoy.config.listener.v3.Listener.DeprecatedV1B\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12@\n\ndrain_type\x18\x08 \x01(\x0e\x32,.envoy.config.listener.v3.Listener.DrainType\x12\x42\n\x10listener_filters\x18\t \x03(\x0b\x32(.envoy.config.listener.v3.ListenerFilter\x12;\n\x18listener_filters_timeout\x18\x0f \x01(\x0b\x32\x19.google.protobuf.Duration\x12,\n$continue_on_listener_filters_timeout\x18\x11 \x01(\x08\x12/\n\x0btransparent\x18\n \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12,\n\x08\x66reebind\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12:\n\x0esocket_options\x18\r \x03(\x0b\x32\".envoy.config.core.v3.SocketOption\x12@\n\x1atcp_fast_open_queue_length\x18\x0c \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12\x41\n\x11traffic_direction\x18\x10 \x01(\x0e\x32&.envoy.config.core.v3.TrafficDirection\x12H\n\x13udp_listener_config\x18\x12 \x01(\x0b\x32+.envoy.config.listener.v3.UdpListenerConfig\x12;\n\x0c\x61pi_listener\x18\x13 \x01(\x0b\x32%.envoy.config.listener.v3.ApiListener\x12]\n\x19\x63onnection_balance_config\x18\x14 \x01(\x0b\x32:.envoy.config.listener.v3.Listener.ConnectionBalanceConfig\x12\x1f\n\nreuse_port\x18\x15 \x01(\x08\x42\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12\x35\n\x11\x65nable_reuse_port\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12\x38\n\naccess_log\x18\x16 \x03(\x0b\x32$.envoy.config.accesslog.v3.AccessLog\x12\x36\n\x10tcp_backlog_size\x18\x18 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12Y\n*max_connections_to_accept_per_socket_event\x18\" \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02 \x00\x12\x30\n\x0c\x62ind_to_port\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12V\n\x11internal_listener\x18\x1b \x01(\x0b\x32\x39.envoy.config.listener.v3.Listener.InternalListenerConfigH\x00\x12\x14\n\x0c\x65nable_mptcp\x18\x1e \x01(\x08\x12 \n\x18ignore_global_conn_limit\x18\x1f \x01(\x08\x12\x1f\n\x17\x62ypass_overload_manager\x18# \x01(\x08\x1ak\n\x0c\x44\x65precatedV1\x12\x30\n\x0c\x62ind_to_port\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.BoolValue:)\x9a\xc5\x88\x1e$\n\"envoy.api.v2.Listener.DeprecatedV1\x1a\xdf\x02\n\x17\x43onnectionBalanceConfig\x12`\n\rexact_balance\x18\x01 \x01(\x0b\x32G.envoy.config.listener.v3.Listener.ConnectionBalanceConfig.ExactBalanceH\x00\x12\x44\n\x0e\x65xtend_balance\x18\x02 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigH\x00\x1aQ\n\x0c\x45xactBalance:A\x9a\xc5\x88\x1e<\n:envoy.api.v2.Listener.ConnectionBalanceConfig.ExactBalance:4\x9a\xc5\x88\x1e/\n-envoy.api.v2.Listener.ConnectionBalanceConfigB\x13\n\x0c\x62\x61lance_type\x12\x03\xf8\x42\x01\x1a\x18\n\x16InternalListenerConfig\")\n\tDrainType\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x0f\n\x0bMODIFY_ONLY\x10\x01:\x1c\x9a\xc5\x88\x1e\x17\n\x15\x65nvoy.api.v2.ListenerB\x14\n\x12listener_specifierJ\x04\x08\x0e\x10\x0fJ\x04\x08\x17\x10\x18\"\x11\n\x0fListenerManager\"\x1b\n\x19ValidationListenerManager\"\x14\n\x12\x41piListenerManagerB\x8d\x01\n&io.envoyproxy.envoy.config.listener.v3B\rListenerProtoP\x01ZJgithub.com/envoyproxy/go-control-plane/envoy/config/listener/v3;listenerv3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.listener.v3.listener_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n&io.envoyproxy.envoy.config.listener.v3B\rListenerProtoP\001ZJgithub.com/envoyproxy/go-control-plane/envoy/config/listener/v3;listenerv3\272\200\310\321\006\002\020\002'
  _LISTENER_DEPRECATEDV1._options = None
  _LISTENER_DEPRECATEDV1._serialized_options = b'\232\305\210\036$\n\"envoy.api.v2.Listener.DeprecatedV1'
  _LISTENER_CONNECTIONBALANCECONFIG_EXACTBALANCE._options = None
  _LISTENER_CONNECTIONBALANCECONFIG_EXACTBALANCE._serialized_options = b'\232\305\210\036<\n:envoy.api.v2.Listener.ConnectionBalanceConfig.ExactBalance'
  _LISTENER_CONNECTIONBALANCECONFIG.oneofs_by_name['balance_type']._options = None
  _LISTENER_CONNECTIONBALANCECONFIG.oneofs_by_name['balance_type']._serialized_options = b'\370B\001'
  _LISTENER_CONNECTIONBALANCECONFIG._options = None
  _LISTENER_CONNECTIONBALANCECONFIG._serialized_options = b'\232\305\210\036/\n-envoy.api.v2.Listener.ConnectionBalanceConfig'
  _LISTENER.fields_by_name['filter_chain_matcher']._options = None
  _LISTENER.fields_by_name['filter_chain_matcher']._serialized_options = b'\322\306\244\341\006\002\010\001'
  _LISTENER.fields_by_name['per_connection_buffer_limit_bytes']._options = None
  _LISTENER.fields_by_name['per_connection_buffer_limit_bytes']._serialized_options = b'\212\223\267*\002\010\001'
  _LISTENER.fields_by_name['deprecated_v1']._options = None
  _LISTENER.fields_by_name['deprecated_v1']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _LISTENER.fields_by_name['reuse_port']._options = None
  _LISTENER.fields_by_name['reuse_port']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _LISTENER.fields_by_name['max_connections_to_accept_per_socket_event']._options = None
  _LISTENER.fields_by_name['max_connections_to_accept_per_socket_event']._serialized_options = b'\372B\004*\002 \000'
  _LISTENER._options = None
  _LISTENER._serialized_options = b'\232\305\210\036\027\n\025envoy.api.v2.Listener'
  _globals['_ADDITIONALADDRESS']._serialized_start=740
  _globals['_ADDITIONALADDRESS']._serialized_end=876
  _globals['_LISTENERCOLLECTION']._serialized_start=878
  _globals['_LISTENERCOLLECTION']._serialized_end=945
  _globals['_LISTENER']._serialized_start=948
  _globals['_LISTENER']._serialized_end=3463
  _globals['_LISTENER_DEPRECATEDV1']._serialized_start=2869
  _globals['_LISTENER_DEPRECATEDV1']._serialized_end=2976
  _globals['_LISTENER_CONNECTIONBALANCECONFIG']._serialized_start=2979
  _globals['_LISTENER_CONNECTIONBALANCECONFIG']._serialized_end=3330
  _globals['_LISTENER_CONNECTIONBALANCECONFIG_EXACTBALANCE']._serialized_start=3174
  _globals['_LISTENER_CONNECTIONBALANCECONFIG_EXACTBALANCE']._serialized_end=3255
  _globals['_LISTENER_INTERNALLISTENERCONFIG']._serialized_start=3332
  _globals['_LISTENER_INTERNALLISTENERCONFIG']._serialized_end=3356
  _globals['_LISTENER_DRAINTYPE']._serialized_start=3358
  _globals['_LISTENER_DRAINTYPE']._serialized_end=3399
  _globals['_LISTENERMANAGER']._serialized_start=3465
  _globals['_LISTENERMANAGER']._serialized_end=3482
  _globals['_VALIDATIONLISTENERMANAGER']._serialized_start=3484
  _globals['_VALIDATIONLISTENERMANAGER']._serialized_end=3511
  _globals['_APILISTENERMANAGER']._serialized_start=3513
  _globals['_APILISTENERMANAGER']._serialized_end=3533
# @@protoc_insertion_point(module_scope)
