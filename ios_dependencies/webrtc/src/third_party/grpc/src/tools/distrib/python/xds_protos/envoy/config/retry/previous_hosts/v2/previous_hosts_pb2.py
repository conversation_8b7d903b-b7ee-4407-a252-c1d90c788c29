# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/retry/previous_hosts/v2/previous_hosts.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9envoy/config/retry/previous_hosts/v2/previous_hosts.proto\x12$envoy.config.retry.previous_hosts.v2\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"\x18\n\x16PreviousHostsPredicateB\xe5\x01\n2io.envoyproxy.envoy.config.retry.previous_hosts.v2B\x12PreviousHostsProtoP\x01Z\\github.com/envoyproxy/go-control-plane/envoy/config/retry/previous_hosts/v2;previous_hostsv2\xf2\x98\xfe\x8f\x05/\x12-envoy.extensions.retry.host.previous_hosts.v3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.retry.previous_hosts.v2.previous_hosts_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n2io.envoyproxy.envoy.config.retry.previous_hosts.v2B\022PreviousHostsProtoP\001Z\\github.com/envoyproxy/go-control-plane/envoy/config/retry/previous_hosts/v2;previous_hostsv2\362\230\376\217\005/\022-envoy.extensions.retry.host.previous_hosts.v3\272\200\310\321\006\002\020\002'
  _globals['_PREVIOUSHOSTSPREDICATE']._serialized_start=162
  _globals['_PREVIOUSHOSTSPREDICATE']._serialized_end=186
# @@protoc_insertion_point(module_scope)
