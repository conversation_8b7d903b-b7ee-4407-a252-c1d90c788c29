# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/dubbo_proxy/v2alpha1/dubbo_proxy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.filter.network.dubbo_proxy.v2alpha1 import route_pb2 as envoy_dot_config_dot_filter_dot_network_dot_dubbo__proxy_dot_v2alpha1_dot_route__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nBenvoy/config/filter/network/dubbo_proxy/v2alpha1/dubbo_proxy.proto\x12\x30\x65nvoy.config.filter.network.dubbo_proxy.v2alpha1\x1a<envoy/config/filter/network/dubbo_proxy/v2alpha1/route.proto\x1a\x19google/protobuf/any.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xa8\x03\n\nDubboProxy\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12_\n\rprotocol_type\x18\x02 \x01(\x0e\x32>.envoy.config.filter.network.dubbo_proxy.v2alpha1.ProtocolTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12i\n\x12serialization_type\x18\x03 \x01(\x0e\x32\x43.envoy.config.filter.network.dubbo_proxy.v2alpha1.SerializationTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12Z\n\x0croute_config\x18\x04 \x03(\x0b\x32\x44.envoy.config.filter.network.dubbo_proxy.v2alpha1.RouteConfiguration\x12T\n\rdubbo_filters\x18\x05 \x03(\x0b\x32=.envoy.config.filter.network.dubbo_proxy.v2alpha1.DubboFilter\"J\n\x0b\x44ubboFilter\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12$\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any*\x19\n\x0cProtocolType\x12\t\n\x05\x44ubbo\x10\x00*!\n\x11SerializationType\x12\x0c\n\x08Hessian2\x10\x00\x42\xeb\x01\n>io.envoyproxy.envoy.config.filter.network.dubbo_proxy.v2alpha1B\x0f\x44ubboProxyProtoP\x01ZWgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/dubbo_proxy/v2alpha1\xf2\x98\xfe\x8f\x05\x31\x12/envoy.extensions.filters.network.dubbo_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.dubbo_proxy.v2alpha1.dubbo_proxy_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n>io.envoyproxy.envoy.config.filter.network.dubbo_proxy.v2alpha1B\017DubboProxyProtoP\001ZWgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/dubbo_proxy/v2alpha1\362\230\376\217\0051\022/envoy.extensions.filters.network.dubbo_proxy.v3\272\200\310\321\006\002\020\001'
  _DUBBOPROXY.fields_by_name['stat_prefix']._options = None
  _DUBBOPROXY.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _DUBBOPROXY.fields_by_name['protocol_type']._options = None
  _DUBBOPROXY.fields_by_name['protocol_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _DUBBOPROXY.fields_by_name['serialization_type']._options = None
  _DUBBOPROXY.fields_by_name['serialization_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _DUBBOFILTER.fields_by_name['name']._options = None
  _DUBBOFILTER.fields_by_name['name']._serialized_options = b'\372B\004r\002 \001'
  _globals['_PROTOCOLTYPE']._serialized_start=800
  _globals['_PROTOCOLTYPE']._serialized_end=825
  _globals['_SERIALIZATIONTYPE']._serialized_start=827
  _globals['_SERIALIZATIONTYPE']._serialized_end=860
  _globals['_DUBBOPROXY']._serialized_start=298
  _globals['_DUBBOPROXY']._serialized_end=722
  _globals['_DUBBOFILTER']._serialized_start=724
  _globals['_DUBBOFILTER']._serialized_end=798
# @@protoc_insertion_point(module_scope)
