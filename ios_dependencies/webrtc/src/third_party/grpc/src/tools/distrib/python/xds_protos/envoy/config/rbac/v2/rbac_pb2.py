# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/rbac/v2/rbac.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import address_pb2 as envoy_dot_api_dot_v2_dot_core_dot_address__pb2
from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from envoy.type.matcher import metadata_pb2 as envoy_dot_type_dot_matcher_dot_metadata__pb2
from envoy.type.matcher import path_pb2 as envoy_dot_type_dot_matcher_dot_path__pb2
from envoy.type.matcher import string_pb2 as envoy_dot_type_dot_matcher_dot_string__pb2
from google.api.expr.v1alpha1 import syntax_pb2 as google_dot_api_dot_expr_dot_v1alpha1_dot_syntax__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1f\x65nvoy/config/rbac/v2/rbac.proto\x12\x14\x65nvoy.config.rbac.v2\x1a\x1f\x65nvoy/api/v2/core/address.proto\x1a)envoy/api/v2/route/route_components.proto\x1a!envoy/type/matcher/metadata.proto\x1a\x1d\x65nvoy/type/matcher/path.proto\x1a\x1f\x65nvoy/type/matcher/string.proto\x1a%google/api/expr/v1alpha1/syntax.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xe3\x01\n\x04RBAC\x12\x31\n\x06\x61\x63tion\x18\x01 \x01(\x0e\x32!.envoy.config.rbac.v2.RBAC.Action\x12:\n\x08policies\x18\x02 \x03(\x0b\x32(.envoy.config.rbac.v2.RBAC.PoliciesEntry\x1aM\n\rPoliciesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12+\n\x05value\x18\x02 \x01(\x0b\x32\x1c.envoy.config.rbac.v2.Policy:\x02\x38\x01\"\x1d\n\x06\x41\x63tion\x12\t\n\x05\x41LLOW\x10\x00\x12\x08\n\x04\x44\x45NY\x10\x01\"\xbb\x01\n\x06Policy\x12?\n\x0bpermissions\x18\x01 \x03(\x0b\x32 .envoy.config.rbac.v2.PermissionB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12=\n\nprincipals\x18\x02 \x03(\x0b\x32\x1f.envoy.config.rbac.v2.PrincipalB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12\x31\n\tcondition\x18\x03 \x01(\x0b\x32\x1e.google.api.expr.v1alpha1.Expr\"\xe4\x04\n\nPermission\x12\x39\n\tand_rules\x18\x01 \x01(\x0b\x32$.envoy.config.rbac.v2.Permission.SetH\x00\x12\x38\n\x08or_rules\x18\x02 \x01(\x0b\x32$.envoy.config.rbac.v2.Permission.SetH\x00\x12\x16\n\x03\x61ny\x18\x03 \x01(\x08\x42\x07\xfa\x42\x04j\x02\x08\x01H\x00\x12\x33\n\x06header\x18\x04 \x01(\x0b\x32!.envoy.api.v2.route.HeaderMatcherH\x00\x12\x33\n\x08url_path\x18\n \x01(\x0b\x32\x1f.envoy.type.matcher.PathMatcherH\x00\x12\x36\n\x0e\x64\x65stination_ip\x18\x05 \x01(\x0b\x32\x1c.envoy.api.v2.core.CidrRangeH\x00\x12%\n\x10\x64\x65stination_port\x18\x06 \x01(\rB\t\xfa\x42\x06*\x04\x18\xff\xff\x03H\x00\x12\x37\n\x08metadata\x18\x07 \x01(\x0b\x32#.envoy.type.matcher.MetadataMatcherH\x00\x12\x34\n\x08not_rule\x18\x08 \x01(\x0b\x32 .envoy.config.rbac.v2.PermissionH\x00\x12\x42\n\x15requested_server_name\x18\t \x01(\x0b\x32!.envoy.type.matcher.StringMatcherH\x00\x1a@\n\x03Set\x12\x39\n\x05rules\x18\x01 \x03(\x0b\x32 .envoy.config.rbac.v2.PermissionB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x42\x0b\n\x04rule\x12\x03\xf8\x42\x01\"\xf8\x05\n\tPrincipal\x12\x36\n\x07\x61nd_ids\x18\x01 \x01(\x0b\x32#.envoy.config.rbac.v2.Principal.SetH\x00\x12\x35\n\x06or_ids\x18\x02 \x01(\x0b\x32#.envoy.config.rbac.v2.Principal.SetH\x00\x12\x16\n\x03\x61ny\x18\x03 \x01(\x08\x42\x07\xfa\x42\x04j\x02\x08\x01H\x00\x12\x46\n\rauthenticated\x18\x04 \x01(\x0b\x32-.envoy.config.rbac.v2.Principal.AuthenticatedH\x00\x12\x35\n\tsource_ip\x18\x05 \x01(\x0b\x32\x1c.envoy.api.v2.core.CidrRangeB\x02\x18\x01H\x00\x12\x38\n\x10\x64irect_remote_ip\x18\n \x01(\x0b\x32\x1c.envoy.api.v2.core.CidrRangeH\x00\x12\x31\n\tremote_ip\x18\x0b \x01(\x0b\x32\x1c.envoy.api.v2.core.CidrRangeH\x00\x12\x33\n\x06header\x18\x06 \x01(\x0b\x32!.envoy.api.v2.route.HeaderMatcherH\x00\x12\x33\n\x08url_path\x18\t \x01(\x0b\x32\x1f.envoy.type.matcher.PathMatcherH\x00\x12\x37\n\x08metadata\x18\x07 \x01(\x0b\x32#.envoy.type.matcher.MetadataMatcherH\x00\x12\x31\n\x06not_id\x18\x08 \x01(\x0b\x32\x1f.envoy.config.rbac.v2.PrincipalH\x00\x1a=\n\x03Set\x12\x36\n\x03ids\x18\x01 \x03(\x0b\x32\x1f.envoy.config.rbac.v2.PrincipalB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1aP\n\rAuthenticated\x12\x39\n\x0eprincipal_name\x18\x02 \x01(\x0b\x32!.envoy.type.matcher.StringMatcherJ\x04\x08\x01\x10\x02\x42\x11\n\nidentifier\x12\x03\xf8\x42\x01\x42}\n\"io.envoyproxy.envoy.config.rbac.v2B\tRbacProtoP\x01ZBgithub.com/envoyproxy/go-control-plane/envoy/config/rbac/v2;rbacv2\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.rbac.v2.rbac_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\"io.envoyproxy.envoy.config.rbac.v2B\tRbacProtoP\001ZBgithub.com/envoyproxy/go-control-plane/envoy/config/rbac/v2;rbacv2\272\200\310\321\006\002\020\001'
  _RBAC_POLICIESENTRY._options = None
  _RBAC_POLICIESENTRY._serialized_options = b'8\001'
  _POLICY.fields_by_name['permissions']._options = None
  _POLICY.fields_by_name['permissions']._serialized_options = b'\372B\005\222\001\002\010\001'
  _POLICY.fields_by_name['principals']._options = None
  _POLICY.fields_by_name['principals']._serialized_options = b'\372B\005\222\001\002\010\001'
  _PERMISSION_SET.fields_by_name['rules']._options = None
  _PERMISSION_SET.fields_by_name['rules']._serialized_options = b'\372B\005\222\001\002\010\001'
  _PERMISSION.oneofs_by_name['rule']._options = None
  _PERMISSION.oneofs_by_name['rule']._serialized_options = b'\370B\001'
  _PERMISSION.fields_by_name['any']._options = None
  _PERMISSION.fields_by_name['any']._serialized_options = b'\372B\004j\002\010\001'
  _PERMISSION.fields_by_name['destination_port']._options = None
  _PERMISSION.fields_by_name['destination_port']._serialized_options = b'\372B\006*\004\030\377\377\003'
  _PRINCIPAL_SET.fields_by_name['ids']._options = None
  _PRINCIPAL_SET.fields_by_name['ids']._serialized_options = b'\372B\005\222\001\002\010\001'
  _PRINCIPAL.oneofs_by_name['identifier']._options = None
  _PRINCIPAL.oneofs_by_name['identifier']._serialized_options = b'\370B\001'
  _PRINCIPAL.fields_by_name['any']._options = None
  _PRINCIPAL.fields_by_name['any']._serialized_options = b'\372B\004j\002\010\001'
  _PRINCIPAL.fields_by_name['source_ip']._options = None
  _PRINCIPAL.fields_by_name['source_ip']._serialized_options = b'\030\001'
  _globals['_RBAC']._serialized_start=328
  _globals['_RBAC']._serialized_end=555
  _globals['_RBAC_POLICIESENTRY']._serialized_start=447
  _globals['_RBAC_POLICIESENTRY']._serialized_end=524
  _globals['_RBAC_ACTION']._serialized_start=526
  _globals['_RBAC_ACTION']._serialized_end=555
  _globals['_POLICY']._serialized_start=558
  _globals['_POLICY']._serialized_end=745
  _globals['_PERMISSION']._serialized_start=748
  _globals['_PERMISSION']._serialized_end=1360
  _globals['_PERMISSION_SET']._serialized_start=1283
  _globals['_PERMISSION_SET']._serialized_end=1347
  _globals['_PRINCIPAL']._serialized_start=1363
  _globals['_PRINCIPAL']._serialized_end=2123
  _globals['_PRINCIPAL_SET']._serialized_start=1961
  _globals['_PRINCIPAL_SET']._serialized_end=2022
  _globals['_PRINCIPAL_AUTHENTICATED']._serialized_start=2024
  _globals['_PRINCIPAL_AUTHENTICATED']._serialized_end=2104
# @@protoc_insertion_point(module_scope)
