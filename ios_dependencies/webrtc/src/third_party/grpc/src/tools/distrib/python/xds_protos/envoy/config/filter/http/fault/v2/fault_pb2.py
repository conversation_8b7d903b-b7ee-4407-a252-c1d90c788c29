# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/fault/v2/fault.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from envoy.config.filter.fault.v2 import fault_pb2 as envoy_dot_config_dot_filter_dot_fault_dot_v2_dot_fault__pb2
from envoy.type import percent_pb2 as envoy_dot_type_dot_percent__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-envoy/config/filter/http/fault/v2/fault.proto\x12!envoy.config.filter.http.fault.v2\x1a)envoy/api/v2/route/route_components.proto\x1a(envoy/config/filter/fault/v2/fault.proto\x1a\x18\x65nvoy/type/percent.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xde\x01\n\nFaultAbort\x12\"\n\x0bhttp_status\x18\x02 \x01(\rB\x0b\xfa\x42\x08*\x06\x10\xd8\x04(\xc8\x01H\x00\x12Q\n\x0cheader_abort\x18\x04 \x01(\x0b\x32\x39.envoy.config.filter.http.fault.v2.FaultAbort.HeaderAbortH\x00\x12\x31\n\npercentage\x18\x03 \x01(\x0b\x32\x1d.envoy.type.FractionalPercent\x1a\r\n\x0bHeaderAbortB\x11\n\nerror_type\x12\x03\xf8\x42\x01J\x04\x08\x01\x10\x02\"\xbf\x04\n\tHTTPFault\x12\x37\n\x05\x64\x65lay\x18\x01 \x01(\x0b\x32(.envoy.config.filter.fault.v2.FaultDelay\x12<\n\x05\x61\x62ort\x18\x02 \x01(\x0b\x32-.envoy.config.filter.http.fault.v2.FaultAbort\x12\x18\n\x10upstream_cluster\x18\x03 \x01(\t\x12\x32\n\x07headers\x18\x04 \x03(\x0b\x32!.envoy.api.v2.route.HeaderMatcher\x12\x18\n\x10\x64ownstream_nodes\x18\x05 \x03(\t\x12\x37\n\x11max_active_faults\x18\x06 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12I\n\x13response_rate_limit\x18\x07 \x01(\x0b\x32,.envoy.config.filter.fault.v2.FaultRateLimit\x12\x1d\n\x15\x64\x65lay_percent_runtime\x18\x08 \x01(\t\x12\x1d\n\x15\x61\x62ort_percent_runtime\x18\t \x01(\t\x12\x1e\n\x16\x64\x65lay_duration_runtime\x18\n \x01(\t\x12!\n\x19\x61\x62ort_http_status_runtime\x18\x0b \x01(\t\x12!\n\x19max_active_faults_runtime\x18\x0c \x01(\t\x12+\n#response_rate_limit_percent_runtime\x18\r \x01(\tB\xc7\x01\n/io.envoyproxy.envoy.config.filter.http.fault.v2B\nFaultProtoP\x01ZPgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/fault/v2;faultv2\xf2\x98\xfe\x8f\x05(\x12&envoy.extensions.filters.http.fault.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.fault.v2.fault_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n/io.envoyproxy.envoy.config.filter.http.fault.v2B\nFaultProtoP\001ZPgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/fault/v2;faultv2\362\230\376\217\005(\022&envoy.extensions.filters.http.fault.v3\272\200\310\321\006\002\020\001'
  _FAULTABORT.oneofs_by_name['error_type']._options = None
  _FAULTABORT.oneofs_by_name['error_type']._serialized_options = b'\370B\001'
  _FAULTABORT.fields_by_name['http_status']._options = None
  _FAULTABORT.fields_by_name['http_status']._serialized_options = b'\372B\010*\006\020\330\004(\310\001'
  _globals['_FAULTABORT']._serialized_start=316
  _globals['_FAULTABORT']._serialized_end=538
  _globals['_FAULTABORT_HEADERABORT']._serialized_start=500
  _globals['_FAULTABORT_HEADERABORT']._serialized_end=513
  _globals['_HTTPFAULT']._serialized_start=541
  _globals['_HTTPFAULT']._serialized_end=1116
# @@protoc_insertion_point(module_scope)
