# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/ip_tagging/v2/ip_tagging.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import address_pb2 as envoy_dot_api_dot_v2_dot_core_dot_address__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7envoy/config/filter/http/ip_tagging/v2/ip_tagging.proto\x12&envoy.config.filter.http.ip_tagging.v2\x1a\x1f\x65nvoy/api/v2/core/address.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xc0\x02\n\tIPTagging\x12]\n\x0crequest_type\x18\x01 \x01(\x0e\x32=.envoy.config.filter.http.ip_tagging.v2.IPTagging.RequestTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12R\n\x07ip_tags\x18\x04 \x03(\x0b\x32\x37.envoy.config.filter.http.ip_tagging.v2.IPTagging.IPTagB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1aK\n\x05IPTag\x12\x13\n\x0bip_tag_name\x18\x01 \x01(\t\x12-\n\x07ip_list\x18\x02 \x03(\x0b\x32\x1c.envoy.api.v2.core.CidrRange\"3\n\x0bRequestType\x12\x08\n\x04\x42OTH\x10\x00\x12\x0c\n\x08INTERNAL\x10\x01\x12\x0c\n\x08\x45XTERNAL\x10\x02\x42\xdf\x01\n4io.envoyproxy.envoy.config.filter.http.ip_tagging.v2B\x0eIpTaggingProtoP\x01ZZgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/ip_tagging/v2;ip_taggingv2\xf2\x98\xfe\x8f\x05-\x12+envoy.extensions.filters.http.ip_tagging.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.ip_tagging.v2.ip_tagging_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n4io.envoyproxy.envoy.config.filter.http.ip_tagging.v2B\016IpTaggingProtoP\001ZZgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/ip_tagging/v2;ip_taggingv2\362\230\376\217\005-\022+envoy.extensions.filters.http.ip_tagging.v3\272\200\310\321\006\002\020\001'
  _IPTAGGING.fields_by_name['request_type']._options = None
  _IPTAGGING.fields_by_name['request_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _IPTAGGING.fields_by_name['ip_tags']._options = None
  _IPTAGGING.fields_by_name['ip_tags']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_IPTAGGING']._serialized_start=221
  _globals['_IPTAGGING']._serialized_end=541
  _globals['_IPTAGGING_IPTAG']._serialized_start=413
  _globals['_IPTAGGING_IPTAG']._serialized_end=488
  _globals['_IPTAGGING_REQUESTTYPE']._serialized_start=490
  _globals['_IPTAGGING_REQUESTTYPE']._serialized_end=541
# @@protoc_insertion_point(module_scope)
