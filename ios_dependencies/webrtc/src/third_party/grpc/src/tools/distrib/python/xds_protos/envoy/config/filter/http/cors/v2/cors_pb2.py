# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/cors/v2/cors.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+envoy/config/filter/http/cors/v2/cors.proto\x12 envoy.config.filter.http.cors.v2\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"\x06\n\x04\x43orsB\xc2\x01\n.io.envoyproxy.envoy.config.filter.http.cors.v2B\tCorsProtoP\x01ZNgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/cors/v2;corsv2\xf2\x98\xfe\x8f\x05\'\x12%envoy.extensions.filters.http.cors.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.cors.v2.cors_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n.io.envoyproxy.envoy.config.filter.http.cors.v2B\tCorsProtoP\001ZNgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/cors/v2;corsv2\362\230\376\217\005\'\022%envoy.extensions.filters.http.cors.v3\272\200\310\321\006\002\020\001'
  _globals['_CORS']._serialized_start=144
  _globals['_CORS']._serialized_end=150
# @@protoc_insertion_point(module_scope)
