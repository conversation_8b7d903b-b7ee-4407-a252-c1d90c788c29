# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/csrf/v2/csrf.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.type.matcher import string_pb2 as envoy_dot_type_dot_matcher_dot_string__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+envoy/config/filter/http/csrf/v2/csrf.proto\x12 envoy.config.filter.http.csrf.v2\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a\x1f\x65nvoy/type/matcher/string.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xdf\x01\n\nCsrfPolicy\x12M\n\x0e\x66ilter_enabled\x18\x01 \x01(\x0b\x32+.envoy.api.v2.core.RuntimeFractionalPercentB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x43\n\x0eshadow_enabled\x18\x02 \x01(\x0b\x32+.envoy.api.v2.core.RuntimeFractionalPercent\x12=\n\x12\x61\x64\x64itional_origins\x18\x03 \x03(\x0b\x32!.envoy.type.matcher.StringMatcherB\xc2\x01\n.io.envoyproxy.envoy.config.filter.http.csrf.v2B\tCsrfProtoP\x01ZNgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/csrf/v2;csrfv2\xf2\x98\xfe\x8f\x05\'\x12%envoy.extensions.filters.http.csrf.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.csrf.v2.csrf_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n.io.envoyproxy.envoy.config.filter.http.csrf.v2B\tCsrfProtoP\001ZNgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/csrf/v2;csrfv2\362\230\376\217\005\'\022%envoy.extensions.filters.http.csrf.v3\272\200\310\321\006\002\020\001'
  _CSRFPOLICY.fields_by_name['filter_enabled']._options = None
  _CSRFPOLICY.fields_by_name['filter_enabled']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CSRFPOLICY']._serialized_start=233
  _globals['_CSRFPOLICY']._serialized_end=456
# @@protoc_insertion_point(module_scope)
