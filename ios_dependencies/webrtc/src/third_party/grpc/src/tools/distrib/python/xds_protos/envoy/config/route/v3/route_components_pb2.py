# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/route/v3/route_components.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.core.v3 import base_pb2 as envoy_dot_config_dot_core_dot_v3_dot_base__pb2
from envoy.config.core.v3 import extension_pb2 as envoy_dot_config_dot_core_dot_v3_dot_extension__pb2
from envoy.config.core.v3 import proxy_protocol_pb2 as envoy_dot_config_dot_core_dot_v3_dot_proxy__protocol__pb2
from envoy.type.matcher.v3 import metadata_pb2 as envoy_dot_type_dot_matcher_dot_v3_dot_metadata__pb2
from envoy.type.matcher.v3 import regex_pb2 as envoy_dot_type_dot_matcher_dot_v3_dot_regex__pb2
from envoy.type.matcher.v3 import string_pb2 as envoy_dot_type_dot_matcher_dot_v3_dot_string__pb2
from envoy.type.metadata.v3 import metadata_pb2 as envoy_dot_type_dot_metadata_dot_v3_dot_metadata__pb2
from envoy.type.tracing.v3 import custom_tag_pb2 as envoy_dot_type_dot_tracing_dot_v3_dot_custom__tag__pb2
from envoy.type.v3 import percent_pb2 as envoy_dot_type_dot_v3_dot_percent__pb2
from envoy.type.v3 import range_pb2 as envoy_dot_type_dot_v3_dot_range__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from xds.type.matcher.v3 import matcher_pb2 as xds_dot_type_dot_matcher_dot_v3_dot_matcher__pb2
from envoy.annotations import deprecation_pb2 as envoy_dot_annotations_dot_deprecation__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,envoy/config/route/v3/route_components.proto\x12\x15\x65nvoy.config.route.v3\x1a\x1f\x65nvoy/config/core/v3/base.proto\x1a$envoy/config/core/v3/extension.proto\x1a)envoy/config/core/v3/proxy_protocol.proto\x1a$envoy/type/matcher/v3/metadata.proto\x1a!envoy/type/matcher/v3/regex.proto\x1a\"envoy/type/matcher/v3/string.proto\x1a%envoy/type/metadata/v3/metadata.proto\x1a&envoy/type/tracing/v3/custom_tag.proto\x1a\x1b\x65nvoy/type/v3/percent.proto\x1a\x19\x65nvoy/type/v3/range.proto\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a!xds/type/matcher/v3/matcher.proto\x1a#envoy/annotations/deprecation.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\xa9\x0c\n\x0bVirtualHost\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12#\n\x07\x64omains\x18\x02 \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\x08\x01\"\x08r\x06\xc0\x01\x02\xc8\x01\x00\x12\x45\n\x06routes\x18\x03 \x03(\x0b\x32\x1c.envoy.config.route.v3.RouteB\x17\xf2\x98\xfe\x8f\x05\x11\x12\x0froute_selection\x12\x46\n\x07matcher\x18\x15 \x01(\x0b\x32\x1c.xds.type.matcher.v3.MatcherB\x17\xf2\x98\xfe\x8f\x05\x11\x12\x0froute_selection\x12T\n\x0brequire_tls\x18\x04 \x01(\x0e\x32\x35.envoy.config.route.v3.VirtualHost.TlsRequirementTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12?\n\x10virtual_clusters\x18\x05 \x03(\x0b\x32%.envoy.config.route.v3.VirtualCluster\x12\x35\n\x0brate_limits\x18\x06 \x03(\x0b\x32 .envoy.config.route.v3.RateLimit\x12R\n\x16request_headers_to_add\x18\x07 \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x35\n\x19request_headers_to_remove\x18\r \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\"\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12S\n\x17response_headers_to_add\x18\n \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x36\n\x1aresponse_headers_to_remove\x18\x0b \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\"\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12<\n\x04\x63ors\x18\x08 \x01(\x0b\x32!.envoy.config.route.v3.CorsPolicyB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12]\n\x17typed_per_filter_config\x18\x0f \x03(\x0b\x32<.envoy.config.route.v3.VirtualHost.TypedPerFilterConfigEntry\x12%\n\x1dinclude_request_attempt_count\x18\x0e \x01(\x08\x12)\n!include_attempt_count_in_response\x18\x13 \x01(\x08\x12\x38\n\x0cretry_policy\x18\x10 \x01(\x0b\x32\".envoy.config.route.v3.RetryPolicy\x12\x37\n\x19retry_policy_typed_config\x18\x14 \x01(\x0b\x32\x14.google.protobuf.Any\x12\x38\n\x0chedge_policy\x18\x11 \x01(\x0b\x32\".envoy.config.route.v3.HedgePolicy\x12\'\n\x1finclude_is_timeout_retry_header\x18\x17 \x01(\x08\x12\x44\n\x1eper_request_buffer_limit_bytes\x18\x12 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12W\n\x17request_mirror_policies\x18\x16 \x03(\x0b\x32\x36.envoy.config.route.v3.RouteAction.RequestMirrorPolicy\x12\x30\n\x08metadata\x18\x18 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x1aQ\n\x19TypedPerFilterConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any:\x02\x38\x01\":\n\x12TlsRequirementType\x12\x08\n\x04NONE\x10\x00\x12\x11\n\rEXTERNAL_ONLY\x10\x01\x12\x07\n\x03\x41LL\x10\x02:%\x9a\xc5\x88\x1e \n\x1e\x65nvoy.api.v2.route.VirtualHostJ\x04\x08\t\x10\nJ\x04\x08\x0c\x10\rR\x11per_filter_config\"\\\n\x0c\x46ilterAction\x12$\n\x06\x61\x63tion\x18\x01 \x01(\x0b\x32\x14.google.protobuf.Any:&\x9a\xc5\x88\x1e!\n\x1f\x65nvoy.api.v2.route.FilterAction\"9\n\tRouteList\x12,\n\x06routes\x18\x01 \x03(\x0b\x32\x1c.envoy.config.route.v3.Route\"\x9a\t\n\x05Route\x12\x0c\n\x04name\x18\x0e \x01(\t\x12:\n\x05match\x18\x01 \x01(\x0b\x32!.envoy.config.route.v3.RouteMatchB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x33\n\x05route\x18\x02 \x01(\x0b\x32\".envoy.config.route.v3.RouteActionH\x00\x12\x39\n\x08redirect\x18\x03 \x01(\x0b\x32%.envoy.config.route.v3.RedirectActionH\x00\x12\x46\n\x0f\x64irect_response\x18\x07 \x01(\x0b\x32+.envoy.config.route.v3.DirectResponseActionH\x00\x12<\n\rfilter_action\x18\x11 \x01(\x0b\x32#.envoy.config.route.v3.FilterActionH\x00\x12K\n\x15non_forwarding_action\x18\x12 \x01(\x0b\x32*.envoy.config.route.v3.NonForwardingActionH\x00\x12\x30\n\x08metadata\x18\x04 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x12\x33\n\tdecorator\x18\x05 \x01(\x0b\x32 .envoy.config.route.v3.Decorator\x12W\n\x17typed_per_filter_config\x18\r \x03(\x0b\x32\x36.envoy.config.route.v3.Route.TypedPerFilterConfigEntry\x12R\n\x16request_headers_to_add\x18\t \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x35\n\x19request_headers_to_remove\x18\x0c \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\"\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12S\n\x17response_headers_to_add\x18\n \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x36\n\x1aresponse_headers_to_remove\x18\x0b \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\"\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12/\n\x07tracing\x18\x0f \x01(\x0b\x32\x1e.envoy.config.route.v3.Tracing\x12\x44\n\x1eper_request_buffer_limit_bytes\x18\x10 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12\x13\n\x0bstat_prefix\x18\x13 \x01(\t\x1aQ\n\x19TypedPerFilterConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any:\x02\x38\x01:\x1f\x9a\xc5\x88\x1e\x1a\n\x18\x65nvoy.api.v2.route.RouteB\r\n\x06\x61\x63tion\x12\x03\xf8\x42\x01J\x04\x08\x06\x10\x07J\x04\x08\x08\x10\tR\x11per_filter_config\"\xff\x08\n\x0fWeightedCluster\x12P\n\x08\x63lusters\x18\x01 \x03(\x0b\x32\x34.envoy.config.route.v3.WeightedCluster.ClusterWeightB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12?\n\x0ctotal_weight\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12\x1a\n\x12runtime_key_prefix\x18\x02 \x01(\t\x12\"\n\x0bheader_name\x18\x04 \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x01\xc8\x01\x00H\x00\x1a\xd3\x06\n\rClusterWeight\x12\'\n\x04name\x18\x01 \x01(\tB\x19\xf2\x98\xfe\x8f\x05\x13\x12\x11\x63luster_specifier\x12<\n\x0e\x63luster_header\x18\x0c \x01(\tB$\xfa\x42\x08r\x06\xc0\x01\x01\xc8\x01\x00\xf2\x98\xfe\x8f\x05\x13\x12\x11\x63luster_specifier\x12,\n\x06weight\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12\x36\n\x0emetadata_match\x18\x03 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x12R\n\x16request_headers_to_add\x18\x04 \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x33\n\x19request_headers_to_remove\x18\t \x03(\tB\x10\xfa\x42\r\x92\x01\n\"\x08r\x06\xc0\x01\x01\xc8\x01\x00\x12S\n\x17response_headers_to_add\x18\x05 \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x34\n\x1aresponse_headers_to_remove\x18\x06 \x03(\tB\x10\xfa\x42\r\x92\x01\n\"\x08r\x06\xc0\x01\x01\xc8\x01\x00\x12o\n\x17typed_per_filter_config\x18\n \x03(\x0b\x32N.envoy.config.route.v3.WeightedCluster.ClusterWeight.TypedPerFilterConfigEntry\x12+\n\x14host_rewrite_literal\x18\x0b \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x02\xc8\x01\x00H\x00\x1aQ\n\x19TypedPerFilterConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any:\x02\x38\x01:7\x9a\xc5\x88\x1e\x32\n0envoy.api.v2.route.WeightedCluster.ClusterWeightB\x18\n\x16host_rewrite_specifierJ\x04\x08\x07\x10\x08J\x04\x08\x08\x10\tR\x11per_filter_config:)\x9a\xc5\x88\x1e$\n\"envoy.api.v2.route.WeightedClusterB\x18\n\x16random_value_specifier\"v\n\x16\x43lusterSpecifierPlugin\x12G\n\textension\x18\x01 \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x13\n\x0bis_optional\x18\x02 \x01(\x08\"\x83\t\n\nRouteMatch\x12\x10\n\x06prefix\x18\x01 \x01(\tH\x00\x12\x0e\n\x04path\x18\x02 \x01(\tH\x00\x12\x43\n\nsafe_regex\x18\n \x01(\x0b\x32#.envoy.type.matcher.v3.RegexMatcherB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01H\x00\x12K\n\x0f\x63onnect_matcher\x18\x0c \x01(\x0b\x32\x30.envoy.config.route.v3.RouteMatch.ConnectMatcherH\x00\x12\x36\n\x15path_separated_prefix\x18\x0e \x01(\tB\x15\xfa\x42\x12r\x10\x32\x0e^[^?#]+[^?#/]$H\x00\x12G\n\x11path_match_policy\x18\x0f \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigH\x00\x12\x32\n\x0e\x63\x61se_sensitive\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12H\n\x10runtime_fraction\x18\t \x01(\x0b\x32..envoy.config.core.v3.RuntimeFractionalPercent\x12\x35\n\x07headers\x18\x06 \x03(\x0b\x32$.envoy.config.route.v3.HeaderMatcher\x12\x46\n\x10query_parameters\x18\x07 \x03(\x0b\x32,.envoy.config.route.v3.QueryParameterMatcher\x12\x45\n\x04grpc\x18\x08 \x01(\x0b\x32\x37.envoy.config.route.v3.RouteMatch.GrpcRouteMatchOptions\x12M\n\x0btls_context\x18\x0b \x01(\x0b\x32\x38.envoy.config.route.v3.RouteMatch.TlsContextMatchOptions\x12@\n\x10\x64ynamic_metadata\x18\r \x03(\x0b\x32&.envoy.type.matcher.v3.MetadataMatcher\x1aS\n\x15GrpcRouteMatchOptions::\x9a\xc5\x88\x1e\x35\n3envoy.api.v2.route.RouteMatch.GrpcRouteMatchOptions\x1a\xb3\x01\n\x16TlsContextMatchOptions\x12-\n\tpresented\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12-\n\tvalidated\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue:;\x9a\xc5\x88\x1e\x36\n4envoy.api.v2.route.RouteMatch.TlsContextMatchOptions\x1a\x10\n\x0e\x43onnectMatcher:$\x9a\xc5\x88\x1e\x1f\n\x1d\x65nvoy.api.v2.route.RouteMatchB\x15\n\x0epath_specifier\x12\x03\xf8\x42\x01J\x04\x08\x05\x10\x06J\x04\x08\x03\x10\x04R\x05regex\"\xf4\x04\n\nCorsPolicy\x12G\n\x19\x61llow_origin_string_match\x18\x0b \x03(\x0b\x32$.envoy.type.matcher.v3.StringMatcher\x12\x15\n\rallow_methods\x18\x02 \x01(\t\x12\x15\n\rallow_headers\x18\x03 \x01(\t\x12\x16\n\x0e\x65xpose_headers\x18\x04 \x01(\t\x12\x0f\n\x07max_age\x18\x05 \x01(\t\x12\x35\n\x11\x61llow_credentials\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12H\n\x0e\x66ilter_enabled\x18\t \x01(\x0b\x32..envoy.config.core.v3.RuntimeFractionalPercentH\x00\x12\x46\n\x0eshadow_enabled\x18\n \x01(\x0b\x32..envoy.config.core.v3.RuntimeFractionalPercent\x12@\n\x1c\x61llow_private_network_access\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12\x43\n\x1f\x66orward_not_matching_preflights\x18\r \x01(\x0b\x32\x1a.google.protobuf.BoolValue:$\x9a\xc5\x88\x1e\x1f\n\x1d\x65nvoy.api.v2.route.CorsPolicyB\x13\n\x11\x65nabled_specifierJ\x04\x08\x01\x10\x02J\x04\x08\x08\x10\tJ\x04\x08\x07\x10\x08R\x0c\x61llow_originR\x12\x61llow_origin_regexR\x07\x65nabled\"\xc7%\n\x0bRouteAction\x12\x1a\n\x07\x63luster\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01H\x00\x12\'\n\x0e\x63luster_header\x18\x02 \x01(\tB\r\xfa\x42\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00H\x00\x12\x43\n\x11weighted_clusters\x18\x03 \x01(\x0b\x32&.envoy.config.route.v3.WeightedClusterH\x00\x12\"\n\x18\x63luster_specifier_plugin\x18% \x01(\tH\x00\x12X\n\x1finline_cluster_specifier_plugin\x18\' \x01(\x0b\x32-.envoy.config.route.v3.ClusterSpecifierPluginH\x00\x12q\n\x1f\x63luster_not_found_response_code\x18\x14 \x01(\x0e\x32>.envoy.config.route.v3.RouteAction.ClusterNotFoundResponseCodeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x36\n\x0emetadata_match\x18\x04 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x12#\n\x0eprefix_rewrite\x18\x05 \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x02\xc8\x01\x00\x12\x45\n\rregex_rewrite\x18  \x01(\x0b\x32..envoy.type.matcher.v3.RegexMatchAndSubstitute\x12G\n\x13path_rewrite_policy\x18) \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfig\x12+\n\x14host_rewrite_literal\x18\x06 \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x02\xc8\x01\x00H\x01\x12\x37\n\x11\x61uto_host_rewrite\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.BoolValueH\x01\x12*\n\x13host_rewrite_header\x18\x1d \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x01\xc8\x01\x00H\x01\x12Q\n\x17host_rewrite_path_regex\x18# \x01(\x0b\x32..envoy.type.matcher.v3.RegexMatchAndSubstituteH\x01\x12\x1f\n\x17\x61ppend_x_forwarded_host\x18& \x01(\x08\x12*\n\x07timeout\x18\x08 \x01(\x0b\x32\x19.google.protobuf.Duration\x12/\n\x0cidle_timeout\x18\x18 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x45\n\x11\x65\x61rly_data_policy\x18( \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfig\x12\x38\n\x0cretry_policy\x18\t \x01(\x0b\x32\".envoy.config.route.v3.RetryPolicy\x12\x37\n\x19retry_policy_typed_config\x18! \x01(\x0b\x32\x14.google.protobuf.Any\x12W\n\x17request_mirror_policies\x18\x1e \x03(\x0b\x32\x36.envoy.config.route.v3.RouteAction.RequestMirrorPolicy\x12\x41\n\x08priority\x18\x0b \x01(\x0e\x32%.envoy.config.core.v3.RoutingPriorityB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x35\n\x0brate_limits\x18\r \x03(\x0b\x32 .envoy.config.route.v3.RateLimit\x12G\n\x16include_vh_rate_limits\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.BoolValueB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12\x42\n\x0bhash_policy\x18\x0f \x03(\x0b\x32-.envoy.config.route.v3.RouteAction.HashPolicy\x12<\n\x04\x63ors\x18\x11 \x01(\x0b\x32!.envoy.config.route.v3.CorsPolicyB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12@\n\x10max_grpc_timeout\x18\x17 \x01(\x0b\x32\x19.google.protobuf.DurationB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12\x43\n\x13grpc_timeout_offset\x18\x1c \x01(\x0b\x32\x19.google.protobuf.DurationB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12I\n\x0fupgrade_configs\x18\x19 \x03(\x0b\x32\x30.envoy.config.route.v3.RouteAction.UpgradeConfig\x12O\n\x18internal_redirect_policy\x18\" \x01(\x0b\x32-.envoy.config.route.v3.InternalRedirectPolicy\x12h\n\x18internal_redirect_action\x18\x1a \x01(\x0e\x32\x39.envoy.config.route.v3.RouteAction.InternalRedirectActionB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12I\n\x16max_internal_redirects\x18\x1f \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\x12\x38\n\x0chedge_policy\x18\x1b \x01(\x0b\x32\".envoy.config.route.v3.HedgePolicy\x12Q\n\x13max_stream_duration\x18$ \x01(\x0b\x32\x34.envoy.config.route.v3.RouteAction.MaxStreamDuration\x1a\xf5\x02\n\x13RequestMirrorPolicy\x12*\n\x07\x63luster\x18\x01 \x01(\tB\x19\xf2\x98\xfe\x8f\x05\x13\x12\x11\x63luster_specifier\x12<\n\x0e\x63luster_header\x18\x05 \x01(\tB$\xfa\x42\x08r\x06\xc0\x01\x01\xc8\x01\x00\xf2\x98\xfe\x8f\x05\x13\x12\x11\x63luster_specifier\x12H\n\x10runtime_fraction\x18\x03 \x01(\x0b\x32..envoy.config.core.v3.RuntimeFractionalPercent\x12\x31\n\rtrace_sampled\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12)\n!disable_shadow_host_suffix_append\x18\x06 \x01(\x08:9\x9a\xc5\x88\x1e\x34\n2envoy.api.v2.route.RouteAction.RequestMirrorPolicyJ\x04\x08\x02\x10\x03R\x0bruntime_key\x1a\xb0\n\n\nHashPolicy\x12\x46\n\x06header\x18\x01 \x01(\x0b\x32\x34.envoy.config.route.v3.RouteAction.HashPolicy.HeaderH\x00\x12\x46\n\x06\x63ookie\x18\x02 \x01(\x0b\x32\x34.envoy.config.route.v3.RouteAction.HashPolicy.CookieH\x00\x12\x63\n\x15\x63onnection_properties\x18\x03 \x01(\x0b\x32\x42.envoy.config.route.v3.RouteAction.HashPolicy.ConnectionPropertiesH\x00\x12W\n\x0fquery_parameter\x18\x05 \x01(\x0b\x32<.envoy.config.route.v3.RouteAction.HashPolicy.QueryParameterH\x00\x12Q\n\x0c\x66ilter_state\x18\x06 \x01(\x0b\x32\x39.envoy.config.route.v3.RouteAction.HashPolicy.FilterStateH\x00\x12\x10\n\x08terminal\x18\x04 \x01(\x08\x1a\xac\x01\n\x06Header\x12\"\n\x0bheader_name\x18\x01 \x01(\tB\r\xfa\x42\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12\x45\n\rregex_rewrite\x18\x02 \x01(\x0b\x32..envoy.type.matcher.v3.RegexMatchAndSubstitute:7\x9a\xc5\x88\x1e\x32\n0envoy.api.v2.route.RouteAction.HashPolicy.Header\x1aR\n\x0f\x43ookieAttribute\x12\x1f\n\x04name\x18\x01 \x01(\tB\x11\xfa\x42\x0er\x0c\x10\x01(\x80\x80\x01\xc0\x01\x01\xc8\x01\x00\x12\x1e\n\x05value\x18\x02 \x01(\tB\x0f\xfa\x42\x0cr\n(\x80\x80\x01\xc0\x01\x02\xc8\x01\x00\x1a\xe1\x01\n\x06\x43ookie\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12&\n\x03ttl\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x0c\n\x04path\x18\x03 \x01(\t\x12Q\n\nattributes\x18\x04 \x03(\x0b\x32=.envoy.config.route.v3.RouteAction.HashPolicy.CookieAttribute:7\x9a\xc5\x88\x1e\x32\n0envoy.api.v2.route.RouteAction.HashPolicy.Cookie\x1ap\n\x14\x43onnectionProperties\x12\x11\n\tsource_ip\x18\x01 \x01(\x08:E\x9a\xc5\x88\x1e@\n>envoy.api.v2.route.RouteAction.HashPolicy.ConnectionProperties\x1ah\n\x0eQueryParameter\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01:?\x9a\xc5\x88\x1e:\n8envoy.api.v2.route.RouteAction.HashPolicy.QueryParameter\x1a\x61\n\x0b\x46ilterState\x12\x14\n\x03key\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01:<\x9a\xc5\x88\x1e\x37\n5envoy.api.v2.route.RouteAction.HashPolicy.FilterState:0\x9a\xc5\x88\x1e+\n)envoy.api.v2.route.RouteAction.HashPolicyB\x17\n\x10policy_specifier\x12\x03\xf8\x42\x01\x1a\xdd\x02\n\rUpgradeConfig\x12#\n\x0cupgrade_type\x18\x01 \x01(\tB\r\xfa\x42\nr\x08\x10\x01\xc0\x01\x02\xc8\x01\x00\x12+\n\x07\x65nabled\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12V\n\x0e\x63onnect_config\x18\x03 \x01(\x0b\x32>.envoy.config.route.v3.RouteAction.UpgradeConfig.ConnectConfig\x1am\n\rConnectConfig\x12H\n\x15proxy_protocol_config\x18\x01 \x01(\x0b\x32).envoy.config.core.v3.ProxyProtocolConfig\x12\x12\n\nallow_post\x18\x02 \x01(\x08:3\x9a\xc5\x88\x1e.\n,envoy.api.v2.route.RouteAction.UpgradeConfig\x1a\xc6\x01\n\x11MaxStreamDuration\x12\x36\n\x13max_stream_duration\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\x12:\n\x17grpc_timeout_header_max\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration\x12=\n\x1agrpc_timeout_header_offset\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\"`\n\x1b\x43lusterNotFoundResponseCode\x12\x17\n\x13SERVICE_UNAVAILABLE\x10\x00\x12\r\n\tNOT_FOUND\x10\x01\x12\x19\n\x15INTERNAL_SERVER_ERROR\x10\x02\"^\n\x16InternalRedirectAction\x12\"\n\x1ePASS_THROUGH_INTERNAL_REDIRECT\x10\x00\x12\x1c\n\x18HANDLE_INTERNAL_REDIRECT\x10\x01\x1a\x02\x18\x01:%\x9a\xc5\x88\x1e \n\x1e\x65nvoy.api.v2.route.RouteActionB\x18\n\x11\x63luster_specifier\x12\x03\xf8\x42\x01\x42\x18\n\x16host_rewrite_specifierJ\x04\x08\x0c\x10\rJ\x04\x08\x12\x10\x13J\x04\x08\x13\x10\x14J\x04\x08\x10\x10\x11J\x04\x08\x16\x10\x17J\x04\x08\x15\x10\x16J\x04\x08\n\x10\x0bR\x15request_mirror_policy\"\xdb\r\n\x0bRetryPolicy\x12\x10\n\x08retry_on\x18\x01 \x01(\t\x12\x46\n\x0bnum_retries\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x13\xf2\x98\xfe\x8f\x05\r\n\x0bmax_retries\x12\x32\n\x0fper_try_timeout\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x37\n\x14per_try_idle_timeout\x18\r \x01(\x0b\x32\x19.google.protobuf.Duration\x12H\n\x0eretry_priority\x18\x04 \x01(\x0b\x32\x30.envoy.config.route.v3.RetryPolicy.RetryPriority\x12S\n\x14retry_host_predicate\x18\x05 \x03(\x0b\x32\x35.envoy.config.route.v3.RetryPolicy.RetryHostPredicate\x12L\n\x18retry_options_predicates\x18\x0c \x03(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfig\x12)\n!host_selection_retry_max_attempts\x18\x06 \x01(\x03\x12\x1e\n\x16retriable_status_codes\x18\x07 \x03(\r\x12G\n\x0eretry_back_off\x18\x08 \x01(\x0b\x32/.envoy.config.route.v3.RetryPolicy.RetryBackOff\x12_\n\x1brate_limited_retry_back_off\x18\x0b \x01(\x0b\x32:.envoy.config.route.v3.RetryPolicy.RateLimitedRetryBackOff\x12?\n\x11retriable_headers\x18\t \x03(\x0b\x32$.envoy.config.route.v3.HeaderMatcher\x12G\n\x19retriable_request_headers\x18\n \x03(\x0b\x32$.envoy.config.route.v3.HeaderMatcher\x1a\xa6\x01\n\rRetryPriority\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00:3\x9a\xc5\x88\x1e.\n,envoy.api.v2.route.RetryPolicy.RetryPriorityB\r\n\x0b\x63onfig_typeJ\x04\x08\x02\x10\x03R\x06\x63onfig\x1a\xb0\x01\n\x12RetryHostPredicate\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00:8\x9a\xc5\x88\x1e\x33\n1envoy.api.v2.route.RetryPolicy.RetryHostPredicateB\r\n\x0b\x63onfig_typeJ\x04\x08\x02\x10\x03R\x06\x63onfig\x1a\xbb\x01\n\x0cRetryBackOff\x12<\n\rbase_interval\x18\x01 \x01(\x0b\x32\x19.google.protobuf.DurationB\n\xfa\x42\x07\xaa\x01\x04\x08\x01*\x00\x12\x39\n\x0cmax_interval\x18\x02 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\xfa\x42\x05\xaa\x01\x02*\x00:2\x9a\xc5\x88\x1e-\n+envoy.api.v2.route.RetryPolicy.RetryBackOff\x1az\n\x0bResetHeader\x12\x1b\n\x04name\x18\x01 \x01(\tB\r\xfa\x42\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12N\n\x06\x66ormat\x18\x02 \x01(\x0e\x32\x34.envoy.config.route.v3.RetryPolicy.ResetHeaderFormatB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x1a\xa5\x01\n\x17RateLimitedRetryBackOff\x12O\n\rreset_headers\x18\x01 \x03(\x0b\x32..envoy.config.route.v3.RetryPolicy.ResetHeaderB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12\x39\n\x0cmax_interval\x18\x02 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\xfa\x42\x05\xaa\x01\x02*\x00\"4\n\x11ResetHeaderFormat\x12\x0b\n\x07SECONDS\x10\x00\x12\x12\n\x0eUNIX_TIMESTAMP\x10\x01:%\x9a\xc5\x88\x1e \n\x1e\x65nvoy.api.v2.route.RetryPolicy\"\xdc\x01\n\x0bHedgePolicy\x12?\n\x10initial_requests\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02(\x01\x12\x43\n\x19\x61\x64\x64itional_request_chance\x18\x02 \x01(\x0b\x32 .envoy.type.v3.FractionalPercent\x12 \n\x18hedge_on_per_try_timeout\x18\x03 \x01(\x08:%\x9a\xc5\x88\x1e \n\x1e\x65nvoy.api.v2.route.HedgePolicy\"\xe1\x04\n\x0eRedirectAction\x12\x18\n\x0ehttps_redirect\x18\x04 \x01(\x08H\x00\x12\x19\n\x0fscheme_redirect\x18\x07 \x01(\tH\x00\x12\"\n\rhost_redirect\x18\x01 \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x02\xc8\x01\x00\x12\x15\n\rport_redirect\x18\x08 \x01(\r\x12$\n\rpath_redirect\x18\x02 \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x02\xc8\x01\x00H\x01\x12%\n\x0eprefix_rewrite\x18\x05 \x01(\tB\x0b\xfa\x42\x08r\x06\xc0\x01\x02\xc8\x01\x00H\x01\x12G\n\rregex_rewrite\x18\t \x01(\x0b\x32..envoy.type.matcher.v3.RegexMatchAndSubstituteH\x01\x12[\n\rresponse_code\x18\x03 \x01(\x0e\x32:.envoy.config.route.v3.RedirectAction.RedirectResponseCodeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x13\n\x0bstrip_query\x18\x06 \x01(\x08\"w\n\x14RedirectResponseCode\x12\x15\n\x11MOVED_PERMANENTLY\x10\x00\x12\t\n\x05\x46OUND\x10\x01\x12\r\n\tSEE_OTHER\x10\x02\x12\x16\n\x12TEMPORARY_REDIRECT\x10\x03\x12\x16\n\x12PERMANENT_REDIRECT\x10\x04:(\x9a\xc5\x88\x1e#\n!envoy.api.v2.route.RedirectActionB\x1a\n\x18scheme_rewrite_specifierB\x18\n\x16path_rewrite_specifier\"\x93\x01\n\x14\x44irectResponseAction\x12\x1b\n\x06status\x18\x01 \x01(\rB\x0b\xfa\x42\x08*\x06\x10\xd8\x04(\xc8\x01\x12.\n\x04\x62ody\x18\x02 \x01(\x0b\x32 .envoy.config.core.v3.DataSource:.\x9a\xc5\x88\x1e)\n\'envoy.api.v2.route.DirectResponseAction\"\x15\n\x13NonForwardingAction\"{\n\tDecorator\x12\x1a\n\toperation\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12-\n\tpropagate\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue:#\x9a\xc5\x88\x1e\x1e\n\x1c\x65nvoy.api.v2.route.Decorator\"\x95\x02\n\x07Tracing\x12\x39\n\x0f\x63lient_sampling\x18\x01 \x01(\x0b\x32 .envoy.type.v3.FractionalPercent\x12\x39\n\x0frandom_sampling\x18\x02 \x01(\x0b\x32 .envoy.type.v3.FractionalPercent\x12:\n\x10overall_sampling\x18\x03 \x01(\x0b\x32 .envoy.type.v3.FractionalPercent\x12\x35\n\x0b\x63ustom_tags\x18\x04 \x03(\x0b\x32 .envoy.type.tracing.v3.CustomTag:!\x9a\xc5\x88\x1e\x1c\n\x1a\x65nvoy.api.v2.route.Tracing\"\xa5\x01\n\x0eVirtualCluster\x12\x35\n\x07headers\x18\x04 \x03(\x0b\x32$.envoy.config.route.v3.HeaderMatcher\x12\x15\n\x04name\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01:(\x9a\xc5\x88\x1e#\n!envoy.api.v2.route.VirtualClusterJ\x04\x08\x01\x10\x02J\x04\x08\x03\x10\x04R\x07patternR\x06method\"\x99\x1b\n\tRateLimit\x12\x34\n\x05stage\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02\x18\n\x12\x13\n\x0b\x64isable_key\x18\x02 \x01(\t\x12\x42\n\x07\x61\x63tions\x18\x03 \x03(\x0b\x32\'.envoy.config.route.v3.RateLimit.ActionB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x12\x38\n\x05limit\x18\x04 \x01(\x0b\x32).envoy.config.route.v3.RateLimit.Override\x12@\n\x0bhits_addend\x18\x05 \x01(\x0b\x32+.envoy.config.route.v3.RateLimit.HitsAddend\x12\x1c\n\x14\x61pply_on_stream_done\x18\x06 \x01(\x08\x1a\xfd\x15\n\x06\x41\x63tion\x12O\n\x0esource_cluster\x18\x01 \x01(\x0b\x32\x35.envoy.config.route.v3.RateLimit.Action.SourceClusterH\x00\x12Y\n\x13\x64\x65stination_cluster\x18\x02 \x01(\x0b\x32:.envoy.config.route.v3.RateLimit.Action.DestinationClusterH\x00\x12Q\n\x0frequest_headers\x18\x03 \x01(\x0b\x32\x36.envoy.config.route.v3.RateLimit.Action.RequestHeadersH\x00\x12S\n\x10query_parameters\x18\x0c \x01(\x0b\x32\x37.envoy.config.route.v3.RateLimit.Action.QueryParametersH\x00\x12O\n\x0eremote_address\x18\x04 \x01(\x0b\x32\x35.envoy.config.route.v3.RateLimit.Action.RemoteAddressH\x00\x12I\n\x0bgeneric_key\x18\x05 \x01(\x0b\x32\x32.envoy.config.route.v3.RateLimit.Action.GenericKeyH\x00\x12V\n\x12header_value_match\x18\x06 \x01(\x0b\x32\x38.envoy.config.route.v3.RateLimit.Action.HeaderValueMatchH\x00\x12\x66\n\x10\x64ynamic_metadata\x18\x07 \x01(\x0b\x32\x37.envoy.config.route.v3.RateLimit.Action.DynamicMetaDataB\x11\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0\xb8\xee\xf2\xd2\x05\x01H\x00\x12\x44\n\x08metadata\x18\x08 \x01(\x0b\x32\x30.envoy.config.route.v3.RateLimit.Action.MetaDataH\x00\x12?\n\textension\x18\t \x01(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfigH\x00\x12\\\n\x15masked_remote_address\x18\n \x01(\x0b\x32;.envoy.config.route.v3.RateLimit.Action.MaskedRemoteAddressH\x00\x12g\n\x1bquery_parameter_value_match\x18\x0b \x01(\x0b\<EMAIL>\x00\x1aI\n\rSourceCluster:8\x9a\xc5\x88\x1e\x33\n1envoy.api.v2.route.RateLimit.Action.SourceCluster\x1aS\n\x12\x44\x65stinationCluster:=\x9a\xc5\x88\x1e\x38\n6envoy.api.v2.route.RateLimit.Action.DestinationCluster\x1a\xa8\x01\n\x0eRequestHeaders\x12\"\n\x0bheader_name\x18\x01 \x01(\tB\r\xfa\x42\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12\x1f\n\x0e\x64\x65scriptor_key\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x16\n\x0eskip_if_absent\x18\x03 \x01(\x08:9\x9a\xc5\x88\x1e\x34\n2envoy.api.v2.route.RateLimit.Action.RequestHeaders\x1aq\n\x0fQueryParameters\x12%\n\x14query_parameter_name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x1f\n\x0e\x64\x65scriptor_key\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x16\n\x0eskip_if_absent\x18\x03 \x01(\x08\x1aI\n\rRemoteAddress:8\x9a\xc5\x88\x1e\x33\n1envoy.api.v2.route.RateLimit.Action.RemoteAddress\x1a\x9c\x01\n\x13MaskedRemoteAddress\x12\x41\n\x12v4_prefix_mask_len\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02\x18 \x12\x42\n\x12v6_prefix_mask_len\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x08\xfa\x42\x05*\x03\x18\x80\x01\x1a~\n\nGenericKey\x12!\n\x10\x64\x65scriptor_value\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x16\n\x0e\x64\x65scriptor_key\x18\x02 \x01(\t:5\x9a\xc5\x88\x1e\x30\n.envoy.api.v2.route.RateLimit.Action.GenericKey\x1a\xfd\x01\n\x10HeaderValueMatch\x12\x16\n\x0e\x64\x65scriptor_key\x18\x04 \x01(\t\x12!\n\x10\x64\x65scriptor_value\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x30\n\x0c\x65xpect_match\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12?\n\x07headers\x18\x03 \x03(\x0b\x32$.envoy.config.route.v3.HeaderMatcherB\x08\xfa\x42\x05\x92\x01\x02\x08\x01:;\x9a\xc5\x88\x1e\x36\n4envoy.api.v2.route.RateLimit.Action.HeaderValueMatch\x1a\x8e\x01\n\x0f\x44ynamicMetaData\x12\x1f\n\x0e\x64\x65scriptor_key\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x43\n\x0cmetadata_key\x18\x02 \x01(\x0b\x32#.envoy.type.metadata.v3.MetadataKeyB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x15\n\rdefault_value\x18\x03 \x01(\t\x1a\x9a\x02\n\x08MetaData\x12\x1f\n\x0e\x64\x65scriptor_key\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x43\n\x0cmetadata_key\x18\x02 \x01(\x0b\x32#.envoy.type.metadata.v3.MetadataKeyB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x15\n\rdefault_value\x18\x03 \x01(\t\x12Q\n\x06source\x18\x04 \x01(\x0e\x32\x37.envoy.config.route.v3.RateLimit.Action.MetaData.SourceB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x16\n\x0eskip_if_absent\x18\x05 \x01(\x08\"&\n\x06Source\x12\x0b\n\x07\x44YNAMIC\x10\x00\x12\x0f\n\x0bROUTE_ENTRY\x10\x01\x1a\xd9\x01\n\x18QueryParameterValueMatch\x12\x16\n\x0e\x64\x65scriptor_key\x18\x04 \x01(\t\x12!\n\x10\x64\x65scriptor_value\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x30\n\x0c\x65xpect_match\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12P\n\x10query_parameters\x18\x03 \x03(\x0b\x32,.envoy.config.route.v3.QueryParameterMatcherB\x08\xfa\x42\x05\x92\x01\x02\x08\x01:*\x9a\xc5\x88\x1e%\n#envoy.api.v2.route.RateLimit.ActionB\x17\n\x10\x61\x63tion_specifier\x12\x03\xf8\x42\x01\x1a\xd4\x01\n\x08Override\x12U\n\x10\x64ynamic_metadata\x18\x01 \x01(\x0b\x32\x39.envoy.config.route.v3.RateLimit.Override.DynamicMetadataH\x00\x1aV\n\x0f\x44ynamicMetadata\x12\x43\n\x0cmetadata_key\x18\x01 \x01(\x0b\x32#.envoy.type.metadata.v3.MetadataKeyB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x42\x19\n\x12override_specifier\x12\x03\xf8\x42\x01\x1ag\n\nHitsAddend\x12\x39\n\x06number\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.UInt64ValueB\x0b\xfa\x42\x08\x32\x06\x18\x80\x94\xeb\xdc\x03\x12\x1e\n\x06\x66ormat\x18\x02 \x01(\tB\x0e\xfa\x42\x0br\t:\x01%B\x01%\xd0\x01\x01:#\x9a\xc5\x88\x1e\x1e\n\x1c\x65nvoy.api.v2.route.RateLimit\"\xcc\x04\n\rHeaderMatcher\x12\x1b\n\x04name\x18\x01 \x01(\tB\r\xfa\x42\nr\x08\x10\x01\xc0\x01\x01\xc8\x01\x00\x12\"\n\x0b\x65xact_match\x18\x04 \x01(\tB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0H\x00\x12L\n\x10safe_regex_match\x18\x0b \x01(\x0b\x32#.envoy.type.matcher.v3.RegexMatcherB\x0b\x18\x01\x92\xc7\x86\xd8\x04\x03\x33.0H\x00\x12\x30\n\x0brange_match\x18\x06 \x01(\x0b\x32\x19.envoy.type.v3.Int64RangeH\x00\x12\x17\n\rpresent_match\x18\x07 \x01(\x08H\x00\x12*\n\x0cprefix_match\x18\t \x01(\tB\x12\x18\x01\xfa\x42\x04r\x02\x10\x01\x92\xc7\x86\xd8\x04\x03\x33.0H\x00\x12*\n\x0csuffix_match\x18\n \x01(\tB\x12\x18\x01\xfa\x42\x04r\x02\x10\x01\x92\xc7\x86\xd8\x04\x03\x33.0H\x00\x12,\n\x0e\x63ontains_match\x18\x0c \x01(\tB\x12\x18\x01\xfa\x42\x04r\x02\x10\x01\x92\xc7\x86\xd8\x04\x03\x33.0H\x00\x12<\n\x0cstring_match\x18\r \x01(\x0b\x32$.envoy.type.matcher.v3.StringMatcherH\x00\x12\x14\n\x0cinvert_match\x18\x08 \x01(\x08\x12%\n\x1dtreat_missing_header_as_empty\x18\x0e \x01(\x08:\'\x9a\xc5\x88\x1e\"\n envoy.api.v2.route.HeaderMatcherB\x18\n\x16header_match_specifierJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04J\x04\x08\x05\x10\x06R\x0bregex_match\"\x80\x02\n\x15QueryParameterMatcher\x12\x18\n\x04name\x18\x01 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01(\x80\x08\x12\x46\n\x0cstring_match\x18\x05 \x01(\x0b\x32$.envoy.type.matcher.v3.StringMatcherB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01H\x00\x12\x17\n\rpresent_match\x18\x06 \x01(\x08H\x00:/\x9a\xc5\x88\x1e*\n(envoy.api.v2.route.QueryParameterMatcherB!\n\x1fquery_parameter_match_specifierJ\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05R\x05valueR\x05regex\"\x9c\x02\n\x16InternalRedirectPolicy\x12<\n\x16max_internal_redirects\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12)\n\x17redirect_response_codes\x18\x02 \x03(\rB\x08\xfa\x42\x05\x92\x01\x02\x10\x05\x12>\n\npredicates\x18\x03 \x03(\x0b\x32*.envoy.config.core.v3.TypedExtensionConfig\x12#\n\x1b\x61llow_cross_scheme_redirect\x18\x04 \x01(\x08\x12\x34\n\x18response_headers_to_copy\x18\x05 \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\x18\x01\"\x08r\x06\xc0\x01\x01\xc8\x01\x00\"[\n\x0c\x46ilterConfig\x12$\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x14.google.protobuf.Any\x12\x13\n\x0bis_optional\x18\x02 \x01(\x08\x12\x10\n\x08\x64isabled\x18\x03 \x01(\x08\x42\x8b\x01\n#io.envoyproxy.envoy.config.route.v3B\x14RouteComponentsProtoP\x01ZDgithub.com/envoyproxy/go-control-plane/envoy/config/route/v3;routev3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.route.v3.route_components_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n#io.envoyproxy.envoy.config.route.v3B\024RouteComponentsProtoP\001ZDgithub.com/envoyproxy/go-control-plane/envoy/config/route/v3;routev3\272\200\310\321\006\002\020\002'
  _VIRTUALHOST_TYPEDPERFILTERCONFIGENTRY._options = None
  _VIRTUALHOST_TYPEDPERFILTERCONFIGENTRY._serialized_options = b'8\001'
  _VIRTUALHOST.fields_by_name['name']._options = None
  _VIRTUALHOST.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _VIRTUALHOST.fields_by_name['domains']._options = None
  _VIRTUALHOST.fields_by_name['domains']._serialized_options = b'\372B\017\222\001\014\010\001\"\010r\006\300\001\002\310\001\000'
  _VIRTUALHOST.fields_by_name['routes']._options = None
  _VIRTUALHOST.fields_by_name['routes']._serialized_options = b'\362\230\376\217\005\021\022\017route_selection'
  _VIRTUALHOST.fields_by_name['matcher']._options = None
  _VIRTUALHOST.fields_by_name['matcher']._serialized_options = b'\362\230\376\217\005\021\022\017route_selection'
  _VIRTUALHOST.fields_by_name['require_tls']._options = None
  _VIRTUALHOST.fields_by_name['require_tls']._serialized_options = b'\372B\005\202\001\002\020\001'
  _VIRTUALHOST.fields_by_name['request_headers_to_add']._options = None
  _VIRTUALHOST.fields_by_name['request_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _VIRTUALHOST.fields_by_name['request_headers_to_remove']._options = None
  _VIRTUALHOST.fields_by_name['request_headers_to_remove']._serialized_options = b'\372B\017\222\001\014\"\nr\010\020\001\300\001\001\310\001\000'
  _VIRTUALHOST.fields_by_name['response_headers_to_add']._options = None
  _VIRTUALHOST.fields_by_name['response_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _VIRTUALHOST.fields_by_name['response_headers_to_remove']._options = None
  _VIRTUALHOST.fields_by_name['response_headers_to_remove']._serialized_options = b'\372B\017\222\001\014\"\nr\010\020\001\300\001\001\310\001\000'
  _VIRTUALHOST.fields_by_name['cors']._options = None
  _VIRTUALHOST.fields_by_name['cors']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _VIRTUALHOST._options = None
  _VIRTUALHOST._serialized_options = b'\232\305\210\036 \n\036envoy.api.v2.route.VirtualHost'
  _FILTERACTION._options = None
  _FILTERACTION._serialized_options = b'\232\305\210\036!\n\037envoy.api.v2.route.FilterAction'
  _ROUTE_TYPEDPERFILTERCONFIGENTRY._options = None
  _ROUTE_TYPEDPERFILTERCONFIGENTRY._serialized_options = b'8\001'
  _ROUTE.oneofs_by_name['action']._options = None
  _ROUTE.oneofs_by_name['action']._serialized_options = b'\370B\001'
  _ROUTE.fields_by_name['match']._options = None
  _ROUTE.fields_by_name['match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTE.fields_by_name['request_headers_to_add']._options = None
  _ROUTE.fields_by_name['request_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _ROUTE.fields_by_name['request_headers_to_remove']._options = None
  _ROUTE.fields_by_name['request_headers_to_remove']._serialized_options = b'\372B\017\222\001\014\"\nr\010\020\001\300\001\001\310\001\000'
  _ROUTE.fields_by_name['response_headers_to_add']._options = None
  _ROUTE.fields_by_name['response_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _ROUTE.fields_by_name['response_headers_to_remove']._options = None
  _ROUTE.fields_by_name['response_headers_to_remove']._serialized_options = b'\372B\017\222\001\014\"\nr\010\020\001\300\001\001\310\001\000'
  _ROUTE._options = None
  _ROUTE._serialized_options = b'\232\305\210\036\032\n\030envoy.api.v2.route.Route'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT_TYPEDPERFILTERCONFIGENTRY._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT_TYPEDPERFILTERCONFIGENTRY._serialized_options = b'8\001'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['name']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['name']._serialized_options = b'\362\230\376\217\005\023\022\021cluster_specifier'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['cluster_header']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['cluster_header']._serialized_options = b'\372B\010r\006\300\001\001\310\001\000\362\230\376\217\005\023\022\021cluster_specifier'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['request_headers_to_add']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['request_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['request_headers_to_remove']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['request_headers_to_remove']._serialized_options = b'\372B\r\222\001\n\"\010r\006\300\001\001\310\001\000'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['response_headers_to_add']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['response_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['response_headers_to_remove']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['response_headers_to_remove']._serialized_options = b'\372B\r\222\001\n\"\010r\006\300\001\001\310\001\000'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['host_rewrite_literal']._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT.fields_by_name['host_rewrite_literal']._serialized_options = b'\372B\010r\006\300\001\002\310\001\000'
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT._options = None
  _WEIGHTEDCLUSTER_CLUSTERWEIGHT._serialized_options = b'\232\305\210\0362\n0envoy.api.v2.route.WeightedCluster.ClusterWeight'
  _WEIGHTEDCLUSTER.fields_by_name['clusters']._options = None
  _WEIGHTEDCLUSTER.fields_by_name['clusters']._serialized_options = b'\372B\005\222\001\002\010\001'
  _WEIGHTEDCLUSTER.fields_by_name['total_weight']._options = None
  _WEIGHTEDCLUSTER.fields_by_name['total_weight']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _WEIGHTEDCLUSTER.fields_by_name['header_name']._options = None
  _WEIGHTEDCLUSTER.fields_by_name['header_name']._serialized_options = b'\372B\010r\006\300\001\001\310\001\000'
  _WEIGHTEDCLUSTER._options = None
  _WEIGHTEDCLUSTER._serialized_options = b'\232\305\210\036$\n\"envoy.api.v2.route.WeightedCluster'
  _CLUSTERSPECIFIERPLUGIN.fields_by_name['extension']._options = None
  _CLUSTERSPECIFIERPLUGIN.fields_by_name['extension']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTEMATCH_GRPCROUTEMATCHOPTIONS._options = None
  _ROUTEMATCH_GRPCROUTEMATCHOPTIONS._serialized_options = b'\232\305\210\0365\n3envoy.api.v2.route.RouteMatch.GrpcRouteMatchOptions'
  _ROUTEMATCH_TLSCONTEXTMATCHOPTIONS._options = None
  _ROUTEMATCH_TLSCONTEXTMATCHOPTIONS._serialized_options = b'\232\305\210\0366\n4envoy.api.v2.route.RouteMatch.TlsContextMatchOptions'
  _ROUTEMATCH.oneofs_by_name['path_specifier']._options = None
  _ROUTEMATCH.oneofs_by_name['path_specifier']._serialized_options = b'\370B\001'
  _ROUTEMATCH.fields_by_name['safe_regex']._options = None
  _ROUTEMATCH.fields_by_name['safe_regex']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTEMATCH.fields_by_name['path_separated_prefix']._options = None
  _ROUTEMATCH.fields_by_name['path_separated_prefix']._serialized_options = b'\372B\022r\0202\016^[^?#]+[^?#/]$'
  _ROUTEMATCH._options = None
  _ROUTEMATCH._serialized_options = b'\232\305\210\036\037\n\035envoy.api.v2.route.RouteMatch'
  _CORSPOLICY._options = None
  _CORSPOLICY._serialized_options = b'\232\305\210\036\037\n\035envoy.api.v2.route.CorsPolicy'
  _ROUTEACTION_REQUESTMIRRORPOLICY.fields_by_name['cluster']._options = None
  _ROUTEACTION_REQUESTMIRRORPOLICY.fields_by_name['cluster']._serialized_options = b'\362\230\376\217\005\023\022\021cluster_specifier'
  _ROUTEACTION_REQUESTMIRRORPOLICY.fields_by_name['cluster_header']._options = None
  _ROUTEACTION_REQUESTMIRRORPOLICY.fields_by_name['cluster_header']._serialized_options = b'\372B\010r\006\300\001\001\310\001\000\362\230\376\217\005\023\022\021cluster_specifier'
  _ROUTEACTION_REQUESTMIRRORPOLICY._options = None
  _ROUTEACTION_REQUESTMIRRORPOLICY._serialized_options = b'\232\305\210\0364\n2envoy.api.v2.route.RouteAction.RequestMirrorPolicy'
  _ROUTEACTION_HASHPOLICY_HEADER.fields_by_name['header_name']._options = None
  _ROUTEACTION_HASHPOLICY_HEADER.fields_by_name['header_name']._serialized_options = b'\372B\nr\010\020\001\300\001\001\310\001\000'
  _ROUTEACTION_HASHPOLICY_HEADER._options = None
  _ROUTEACTION_HASHPOLICY_HEADER._serialized_options = b'\232\305\210\0362\n0envoy.api.v2.route.RouteAction.HashPolicy.Header'
  _ROUTEACTION_HASHPOLICY_COOKIEATTRIBUTE.fields_by_name['name']._options = None
  _ROUTEACTION_HASHPOLICY_COOKIEATTRIBUTE.fields_by_name['name']._serialized_options = b'\372B\016r\014\020\001(\200\200\001\300\001\001\310\001\000'
  _ROUTEACTION_HASHPOLICY_COOKIEATTRIBUTE.fields_by_name['value']._options = None
  _ROUTEACTION_HASHPOLICY_COOKIEATTRIBUTE.fields_by_name['value']._serialized_options = b'\372B\014r\n(\200\200\001\300\001\002\310\001\000'
  _ROUTEACTION_HASHPOLICY_COOKIE.fields_by_name['name']._options = None
  _ROUTEACTION_HASHPOLICY_COOKIE.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _ROUTEACTION_HASHPOLICY_COOKIE._options = None
  _ROUTEACTION_HASHPOLICY_COOKIE._serialized_options = b'\232\305\210\0362\n0envoy.api.v2.route.RouteAction.HashPolicy.Cookie'
  _ROUTEACTION_HASHPOLICY_CONNECTIONPROPERTIES._options = None
  _ROUTEACTION_HASHPOLICY_CONNECTIONPROPERTIES._serialized_options = b'\232\305\210\036@\n>envoy.api.v2.route.RouteAction.HashPolicy.ConnectionProperties'
  _ROUTEACTION_HASHPOLICY_QUERYPARAMETER.fields_by_name['name']._options = None
  _ROUTEACTION_HASHPOLICY_QUERYPARAMETER.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _ROUTEACTION_HASHPOLICY_QUERYPARAMETER._options = None
  _ROUTEACTION_HASHPOLICY_QUERYPARAMETER._serialized_options = b'\232\305\210\036:\n8envoy.api.v2.route.RouteAction.HashPolicy.QueryParameter'
  _ROUTEACTION_HASHPOLICY_FILTERSTATE.fields_by_name['key']._options = None
  _ROUTEACTION_HASHPOLICY_FILTERSTATE.fields_by_name['key']._serialized_options = b'\372B\004r\002\020\001'
  _ROUTEACTION_HASHPOLICY_FILTERSTATE._options = None
  _ROUTEACTION_HASHPOLICY_FILTERSTATE._serialized_options = b'\232\305\210\0367\n5envoy.api.v2.route.RouteAction.HashPolicy.FilterState'
  _ROUTEACTION_HASHPOLICY.oneofs_by_name['policy_specifier']._options = None
  _ROUTEACTION_HASHPOLICY.oneofs_by_name['policy_specifier']._serialized_options = b'\370B\001'
  _ROUTEACTION_HASHPOLICY._options = None
  _ROUTEACTION_HASHPOLICY._serialized_options = b'\232\305\210\036+\n)envoy.api.v2.route.RouteAction.HashPolicy'
  _ROUTEACTION_UPGRADECONFIG.fields_by_name['upgrade_type']._options = None
  _ROUTEACTION_UPGRADECONFIG.fields_by_name['upgrade_type']._serialized_options = b'\372B\nr\010\020\001\300\001\002\310\001\000'
  _ROUTEACTION_UPGRADECONFIG._options = None
  _ROUTEACTION_UPGRADECONFIG._serialized_options = b'\232\305\210\036.\n,envoy.api.v2.route.RouteAction.UpgradeConfig'
  _ROUTEACTION.oneofs_by_name['cluster_specifier']._options = None
  _ROUTEACTION.oneofs_by_name['cluster_specifier']._serialized_options = b'\370B\001'
  _ROUTEACTION_INTERNALREDIRECTACTION._options = None
  _ROUTEACTION_INTERNALREDIRECTACTION._serialized_options = b'\030\001'
  _ROUTEACTION.fields_by_name['cluster']._options = None
  _ROUTEACTION.fields_by_name['cluster']._serialized_options = b'\372B\004r\002\020\001'
  _ROUTEACTION.fields_by_name['cluster_header']._options = None
  _ROUTEACTION.fields_by_name['cluster_header']._serialized_options = b'\372B\nr\010\020\001\300\001\001\310\001\000'
  _ROUTEACTION.fields_by_name['cluster_not_found_response_code']._options = None
  _ROUTEACTION.fields_by_name['cluster_not_found_response_code']._serialized_options = b'\372B\005\202\001\002\020\001'
  _ROUTEACTION.fields_by_name['prefix_rewrite']._options = None
  _ROUTEACTION.fields_by_name['prefix_rewrite']._serialized_options = b'\372B\010r\006\300\001\002\310\001\000'
  _ROUTEACTION.fields_by_name['host_rewrite_literal']._options = None
  _ROUTEACTION.fields_by_name['host_rewrite_literal']._serialized_options = b'\372B\010r\006\300\001\002\310\001\000'
  _ROUTEACTION.fields_by_name['host_rewrite_header']._options = None
  _ROUTEACTION.fields_by_name['host_rewrite_header']._serialized_options = b'\372B\010r\006\300\001\001\310\001\000'
  _ROUTEACTION.fields_by_name['priority']._options = None
  _ROUTEACTION.fields_by_name['priority']._serialized_options = b'\372B\005\202\001\002\020\001'
  _ROUTEACTION.fields_by_name['include_vh_rate_limits']._options = None
  _ROUTEACTION.fields_by_name['include_vh_rate_limits']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _ROUTEACTION.fields_by_name['cors']._options = None
  _ROUTEACTION.fields_by_name['cors']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _ROUTEACTION.fields_by_name['max_grpc_timeout']._options = None
  _ROUTEACTION.fields_by_name['max_grpc_timeout']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _ROUTEACTION.fields_by_name['grpc_timeout_offset']._options = None
  _ROUTEACTION.fields_by_name['grpc_timeout_offset']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _ROUTEACTION.fields_by_name['internal_redirect_action']._options = None
  _ROUTEACTION.fields_by_name['internal_redirect_action']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _ROUTEACTION.fields_by_name['max_internal_redirects']._options = None
  _ROUTEACTION.fields_by_name['max_internal_redirects']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _ROUTEACTION._options = None
  _ROUTEACTION._serialized_options = b'\232\305\210\036 \n\036envoy.api.v2.route.RouteAction'
  _RETRYPOLICY_RETRYPRIORITY.fields_by_name['name']._options = None
  _RETRYPOLICY_RETRYPRIORITY.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _RETRYPOLICY_RETRYPRIORITY._options = None
  _RETRYPOLICY_RETRYPRIORITY._serialized_options = b'\232\305\210\036.\n,envoy.api.v2.route.RetryPolicy.RetryPriority'
  _RETRYPOLICY_RETRYHOSTPREDICATE.fields_by_name['name']._options = None
  _RETRYPOLICY_RETRYHOSTPREDICATE.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _RETRYPOLICY_RETRYHOSTPREDICATE._options = None
  _RETRYPOLICY_RETRYHOSTPREDICATE._serialized_options = b'\232\305\210\0363\n1envoy.api.v2.route.RetryPolicy.RetryHostPredicate'
  _RETRYPOLICY_RETRYBACKOFF.fields_by_name['base_interval']._options = None
  _RETRYPOLICY_RETRYBACKOFF.fields_by_name['base_interval']._serialized_options = b'\372B\007\252\001\004\010\001*\000'
  _RETRYPOLICY_RETRYBACKOFF.fields_by_name['max_interval']._options = None
  _RETRYPOLICY_RETRYBACKOFF.fields_by_name['max_interval']._serialized_options = b'\372B\005\252\001\002*\000'
  _RETRYPOLICY_RETRYBACKOFF._options = None
  _RETRYPOLICY_RETRYBACKOFF._serialized_options = b'\232\305\210\036-\n+envoy.api.v2.route.RetryPolicy.RetryBackOff'
  _RETRYPOLICY_RESETHEADER.fields_by_name['name']._options = None
  _RETRYPOLICY_RESETHEADER.fields_by_name['name']._serialized_options = b'\372B\nr\010\020\001\300\001\001\310\001\000'
  _RETRYPOLICY_RESETHEADER.fields_by_name['format']._options = None
  _RETRYPOLICY_RESETHEADER.fields_by_name['format']._serialized_options = b'\372B\005\202\001\002\020\001'
  _RETRYPOLICY_RATELIMITEDRETRYBACKOFF.fields_by_name['reset_headers']._options = None
  _RETRYPOLICY_RATELIMITEDRETRYBACKOFF.fields_by_name['reset_headers']._serialized_options = b'\372B\005\222\001\002\010\001'
  _RETRYPOLICY_RATELIMITEDRETRYBACKOFF.fields_by_name['max_interval']._options = None
  _RETRYPOLICY_RATELIMITEDRETRYBACKOFF.fields_by_name['max_interval']._serialized_options = b'\372B\005\252\001\002*\000'
  _RETRYPOLICY.fields_by_name['num_retries']._options = None
  _RETRYPOLICY.fields_by_name['num_retries']._serialized_options = b'\362\230\376\217\005\r\n\013max_retries'
  _RETRYPOLICY._options = None
  _RETRYPOLICY._serialized_options = b'\232\305\210\036 \n\036envoy.api.v2.route.RetryPolicy'
  _HEDGEPOLICY.fields_by_name['initial_requests']._options = None
  _HEDGEPOLICY.fields_by_name['initial_requests']._serialized_options = b'\372B\004*\002(\001'
  _HEDGEPOLICY._options = None
  _HEDGEPOLICY._serialized_options = b'\232\305\210\036 \n\036envoy.api.v2.route.HedgePolicy'
  _REDIRECTACTION.fields_by_name['host_redirect']._options = None
  _REDIRECTACTION.fields_by_name['host_redirect']._serialized_options = b'\372B\010r\006\300\001\002\310\001\000'
  _REDIRECTACTION.fields_by_name['path_redirect']._options = None
  _REDIRECTACTION.fields_by_name['path_redirect']._serialized_options = b'\372B\010r\006\300\001\002\310\001\000'
  _REDIRECTACTION.fields_by_name['prefix_rewrite']._options = None
  _REDIRECTACTION.fields_by_name['prefix_rewrite']._serialized_options = b'\372B\010r\006\300\001\002\310\001\000'
  _REDIRECTACTION.fields_by_name['response_code']._options = None
  _REDIRECTACTION.fields_by_name['response_code']._serialized_options = b'\372B\005\202\001\002\020\001'
  _REDIRECTACTION._options = None
  _REDIRECTACTION._serialized_options = b'\232\305\210\036#\n!envoy.api.v2.route.RedirectAction'
  _DIRECTRESPONSEACTION.fields_by_name['status']._options = None
  _DIRECTRESPONSEACTION.fields_by_name['status']._serialized_options = b'\372B\010*\006\020\330\004(\310\001'
  _DIRECTRESPONSEACTION._options = None
  _DIRECTRESPONSEACTION._serialized_options = b'\232\305\210\036)\n\'envoy.api.v2.route.DirectResponseAction'
  _DECORATOR.fields_by_name['operation']._options = None
  _DECORATOR.fields_by_name['operation']._serialized_options = b'\372B\004r\002\020\001'
  _DECORATOR._options = None
  _DECORATOR._serialized_options = b'\232\305\210\036\036\n\034envoy.api.v2.route.Decorator'
  _TRACING._options = None
  _TRACING._serialized_options = b'\232\305\210\036\034\n\032envoy.api.v2.route.Tracing'
  _VIRTUALCLUSTER.fields_by_name['name']._options = None
  _VIRTUALCLUSTER.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _VIRTUALCLUSTER._options = None
  _VIRTUALCLUSTER._serialized_options = b'\232\305\210\036#\n!envoy.api.v2.route.VirtualCluster'
  _RATELIMIT_ACTION_SOURCECLUSTER._options = None
  _RATELIMIT_ACTION_SOURCECLUSTER._serialized_options = b'\232\305\210\0363\n1envoy.api.v2.route.RateLimit.Action.SourceCluster'
  _RATELIMIT_ACTION_DESTINATIONCLUSTER._options = None
  _RATELIMIT_ACTION_DESTINATIONCLUSTER._serialized_options = b'\232\305\210\0368\n6envoy.api.v2.route.RateLimit.Action.DestinationCluster'
  _RATELIMIT_ACTION_REQUESTHEADERS.fields_by_name['header_name']._options = None
  _RATELIMIT_ACTION_REQUESTHEADERS.fields_by_name['header_name']._serialized_options = b'\372B\nr\010\020\001\300\001\001\310\001\000'
  _RATELIMIT_ACTION_REQUESTHEADERS.fields_by_name['descriptor_key']._options = None
  _RATELIMIT_ACTION_REQUESTHEADERS.fields_by_name['descriptor_key']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_REQUESTHEADERS._options = None
  _RATELIMIT_ACTION_REQUESTHEADERS._serialized_options = b'\232\305\210\0364\n2envoy.api.v2.route.RateLimit.Action.RequestHeaders'
  _RATELIMIT_ACTION_QUERYPARAMETERS.fields_by_name['query_parameter_name']._options = None
  _RATELIMIT_ACTION_QUERYPARAMETERS.fields_by_name['query_parameter_name']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_QUERYPARAMETERS.fields_by_name['descriptor_key']._options = None
  _RATELIMIT_ACTION_QUERYPARAMETERS.fields_by_name['descriptor_key']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_REMOTEADDRESS._options = None
  _RATELIMIT_ACTION_REMOTEADDRESS._serialized_options = b'\232\305\210\0363\n1envoy.api.v2.route.RateLimit.Action.RemoteAddress'
  _RATELIMIT_ACTION_MASKEDREMOTEADDRESS.fields_by_name['v4_prefix_mask_len']._options = None
  _RATELIMIT_ACTION_MASKEDREMOTEADDRESS.fields_by_name['v4_prefix_mask_len']._serialized_options = b'\372B\004*\002\030 '
  _RATELIMIT_ACTION_MASKEDREMOTEADDRESS.fields_by_name['v6_prefix_mask_len']._options = None
  _RATELIMIT_ACTION_MASKEDREMOTEADDRESS.fields_by_name['v6_prefix_mask_len']._serialized_options = b'\372B\005*\003\030\200\001'
  _RATELIMIT_ACTION_GENERICKEY.fields_by_name['descriptor_value']._options = None
  _RATELIMIT_ACTION_GENERICKEY.fields_by_name['descriptor_value']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_GENERICKEY._options = None
  _RATELIMIT_ACTION_GENERICKEY._serialized_options = b'\232\305\210\0360\n.envoy.api.v2.route.RateLimit.Action.GenericKey'
  _RATELIMIT_ACTION_HEADERVALUEMATCH.fields_by_name['descriptor_value']._options = None
  _RATELIMIT_ACTION_HEADERVALUEMATCH.fields_by_name['descriptor_value']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_HEADERVALUEMATCH.fields_by_name['headers']._options = None
  _RATELIMIT_ACTION_HEADERVALUEMATCH.fields_by_name['headers']._serialized_options = b'\372B\005\222\001\002\010\001'
  _RATELIMIT_ACTION_HEADERVALUEMATCH._options = None
  _RATELIMIT_ACTION_HEADERVALUEMATCH._serialized_options = b'\232\305\210\0366\n4envoy.api.v2.route.RateLimit.Action.HeaderValueMatch'
  _RATELIMIT_ACTION_DYNAMICMETADATA.fields_by_name['descriptor_key']._options = None
  _RATELIMIT_ACTION_DYNAMICMETADATA.fields_by_name['descriptor_key']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_DYNAMICMETADATA.fields_by_name['metadata_key']._options = None
  _RATELIMIT_ACTION_DYNAMICMETADATA.fields_by_name['metadata_key']._serialized_options = b'\372B\005\212\001\002\020\001'
  _RATELIMIT_ACTION_METADATA.fields_by_name['descriptor_key']._options = None
  _RATELIMIT_ACTION_METADATA.fields_by_name['descriptor_key']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_METADATA.fields_by_name['metadata_key']._options = None
  _RATELIMIT_ACTION_METADATA.fields_by_name['metadata_key']._serialized_options = b'\372B\005\212\001\002\020\001'
  _RATELIMIT_ACTION_METADATA.fields_by_name['source']._options = None
  _RATELIMIT_ACTION_METADATA.fields_by_name['source']._serialized_options = b'\372B\005\202\001\002\020\001'
  _RATELIMIT_ACTION_QUERYPARAMETERVALUEMATCH.fields_by_name['descriptor_value']._options = None
  _RATELIMIT_ACTION_QUERYPARAMETERVALUEMATCH.fields_by_name['descriptor_value']._serialized_options = b'\372B\004r\002\020\001'
  _RATELIMIT_ACTION_QUERYPARAMETERVALUEMATCH.fields_by_name['query_parameters']._options = None
  _RATELIMIT_ACTION_QUERYPARAMETERVALUEMATCH.fields_by_name['query_parameters']._serialized_options = b'\372B\005\222\001\002\010\001'
  _RATELIMIT_ACTION.oneofs_by_name['action_specifier']._options = None
  _RATELIMIT_ACTION.oneofs_by_name['action_specifier']._serialized_options = b'\370B\001'
  _RATELIMIT_ACTION.fields_by_name['dynamic_metadata']._options = None
  _RATELIMIT_ACTION.fields_by_name['dynamic_metadata']._serialized_options = b'\030\001\222\307\206\330\004\0033.0\270\356\362\322\005\001'
  _RATELIMIT_ACTION._options = None
  _RATELIMIT_ACTION._serialized_options = b'\232\305\210\036%\n#envoy.api.v2.route.RateLimit.Action'
  _RATELIMIT_OVERRIDE_DYNAMICMETADATA.fields_by_name['metadata_key']._options = None
  _RATELIMIT_OVERRIDE_DYNAMICMETADATA.fields_by_name['metadata_key']._serialized_options = b'\372B\005\212\001\002\020\001'
  _RATELIMIT_OVERRIDE.oneofs_by_name['override_specifier']._options = None
  _RATELIMIT_OVERRIDE.oneofs_by_name['override_specifier']._serialized_options = b'\370B\001'
  _RATELIMIT_HITSADDEND.fields_by_name['number']._options = None
  _RATELIMIT_HITSADDEND.fields_by_name['number']._serialized_options = b'\372B\0102\006\030\200\224\353\334\003'
  _RATELIMIT_HITSADDEND.fields_by_name['format']._options = None
  _RATELIMIT_HITSADDEND.fields_by_name['format']._serialized_options = b'\372B\013r\t:\001%B\001%\320\001\001'
  _RATELIMIT.fields_by_name['stage']._options = None
  _RATELIMIT.fields_by_name['stage']._serialized_options = b'\372B\004*\002\030\n'
  _RATELIMIT.fields_by_name['actions']._options = None
  _RATELIMIT.fields_by_name['actions']._serialized_options = b'\372B\005\222\001\002\010\001'
  _RATELIMIT._options = None
  _RATELIMIT._serialized_options = b'\232\305\210\036\036\n\034envoy.api.v2.route.RateLimit'
  _HEADERMATCHER.fields_by_name['name']._options = None
  _HEADERMATCHER.fields_by_name['name']._serialized_options = b'\372B\nr\010\020\001\300\001\001\310\001\000'
  _HEADERMATCHER.fields_by_name['exact_match']._options = None
  _HEADERMATCHER.fields_by_name['exact_match']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _HEADERMATCHER.fields_by_name['safe_regex_match']._options = None
  _HEADERMATCHER.fields_by_name['safe_regex_match']._serialized_options = b'\030\001\222\307\206\330\004\0033.0'
  _HEADERMATCHER.fields_by_name['prefix_match']._options = None
  _HEADERMATCHER.fields_by_name['prefix_match']._serialized_options = b'\030\001\372B\004r\002\020\001\222\307\206\330\004\0033.0'
  _HEADERMATCHER.fields_by_name['suffix_match']._options = None
  _HEADERMATCHER.fields_by_name['suffix_match']._serialized_options = b'\030\001\372B\004r\002\020\001\222\307\206\330\004\0033.0'
  _HEADERMATCHER.fields_by_name['contains_match']._options = None
  _HEADERMATCHER.fields_by_name['contains_match']._serialized_options = b'\030\001\372B\004r\002\020\001\222\307\206\330\004\0033.0'
  _HEADERMATCHER._options = None
  _HEADERMATCHER._serialized_options = b'\232\305\210\036\"\n envoy.api.v2.route.HeaderMatcher'
  _QUERYPARAMETERMATCHER.fields_by_name['name']._options = None
  _QUERYPARAMETERMATCHER.fields_by_name['name']._serialized_options = b'\372B\007r\005\020\001(\200\010'
  _QUERYPARAMETERMATCHER.fields_by_name['string_match']._options = None
  _QUERYPARAMETERMATCHER.fields_by_name['string_match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _QUERYPARAMETERMATCHER._options = None
  _QUERYPARAMETERMATCHER._serialized_options = b'\232\305\210\036*\n(envoy.api.v2.route.QueryParameterMatcher'
  _INTERNALREDIRECTPOLICY.fields_by_name['redirect_response_codes']._options = None
  _INTERNALREDIRECTPOLICY.fields_by_name['redirect_response_codes']._serialized_options = b'\372B\005\222\001\002\020\005'
  _INTERNALREDIRECTPOLICY.fields_by_name['response_headers_to_copy']._options = None
  _INTERNALREDIRECTPOLICY.fields_by_name['response_headers_to_copy']._serialized_options = b'\372B\017\222\001\014\030\001\"\010r\006\300\001\001\310\001\000'
  _globals['_VIRTUALHOST']._serialized_start=716
  _globals['_VIRTUALHOST']._serialized_end=2293
  _globals['_VIRTUALHOST_TYPEDPERFILTERCONFIGENTRY']._serialized_start=2082
  _globals['_VIRTUALHOST_TYPEDPERFILTERCONFIGENTRY']._serialized_end=2163
  _globals['_VIRTUALHOST_TLSREQUIREMENTTYPE']._serialized_start=2165
  _globals['_VIRTUALHOST_TLSREQUIREMENTTYPE']._serialized_end=2223
  _globals['_FILTERACTION']._serialized_start=2295
  _globals['_FILTERACTION']._serialized_end=2387
  _globals['_ROUTELIST']._serialized_start=2389
  _globals['_ROUTELIST']._serialized_end=2446
  _globals['_ROUTE']._serialized_start=2449
  _globals['_ROUTE']._serialized_end=3627
  _globals['_ROUTE_TYPEDPERFILTERCONFIGENTRY']._serialized_start=2082
  _globals['_ROUTE_TYPEDPERFILTERCONFIGENTRY']._serialized_end=2163
  _globals['_WEIGHTEDCLUSTER']._serialized_start=3630
  _globals['_WEIGHTEDCLUSTER']._serialized_end=4781
  _globals['_WEIGHTEDCLUSTER_CLUSTERWEIGHT']._serialized_start=3861
  _globals['_WEIGHTEDCLUSTER_CLUSTERWEIGHT']._serialized_end=4712
  _globals['_WEIGHTEDCLUSTER_CLUSTERWEIGHT_TYPEDPERFILTERCONFIGENTRY']._serialized_start=2082
  _globals['_WEIGHTEDCLUSTER_CLUSTERWEIGHT_TYPEDPERFILTERCONFIGENTRY']._serialized_end=2163
  _globals['_CLUSTERSPECIFIERPLUGIN']._serialized_start=4783
  _globals['_CLUSTERSPECIFIERPLUGIN']._serialized_end=4901
  _globals['_ROUTEMATCH']._serialized_start=4904
  _globals['_ROUTEMATCH']._serialized_end=6059
  _globals['_ROUTEMATCH_GRPCROUTEMATCHOPTIONS']._serialized_start=5696
  _globals['_ROUTEMATCH_GRPCROUTEMATCHOPTIONS']._serialized_end=5779
  _globals['_ROUTEMATCH_TLSCONTEXTMATCHOPTIONS']._serialized_start=5782
  _globals['_ROUTEMATCH_TLSCONTEXTMATCHOPTIONS']._serialized_end=5961
  _globals['_ROUTEMATCH_CONNECTMATCHER']._serialized_start=5963
  _globals['_ROUTEMATCH_CONNECTMATCHER']._serialized_end=5979
  _globals['_CORSPOLICY']._serialized_start=6062
  _globals['_CORSPOLICY']._serialized_end=6690
  _globals['_ROUTEACTION']._serialized_start=6693
  _globals['_ROUTEACTION']._serialized_end=11500
  _globals['_ROUTEACTION_REQUESTMIRRORPOLICY']._serialized_start=8893
  _globals['_ROUTEACTION_REQUESTMIRRORPOLICY']._serialized_end=9266
  _globals['_ROUTEACTION_HASHPOLICY']._serialized_start=9269
  _globals['_ROUTEACTION_HASHPOLICY']._serialized_end=10597
  _globals['_ROUTEACTION_HASHPOLICY_HEADER']._serialized_start=9719
  _globals['_ROUTEACTION_HASHPOLICY_HEADER']._serialized_end=9891
  _globals['_ROUTEACTION_HASHPOLICY_COOKIEATTRIBUTE']._serialized_start=9893
  _globals['_ROUTEACTION_HASHPOLICY_COOKIEATTRIBUTE']._serialized_end=9975
  _globals['_ROUTEACTION_HASHPOLICY_COOKIE']._serialized_start=9978
  _globals['_ROUTEACTION_HASHPOLICY_COOKIE']._serialized_end=10203
  _globals['_ROUTEACTION_HASHPOLICY_CONNECTIONPROPERTIES']._serialized_start=10205
  _globals['_ROUTEACTION_HASHPOLICY_CONNECTIONPROPERTIES']._serialized_end=10317
  _globals['_ROUTEACTION_HASHPOLICY_QUERYPARAMETER']._serialized_start=10319
  _globals['_ROUTEACTION_HASHPOLICY_QUERYPARAMETER']._serialized_end=10423
  _globals['_ROUTEACTION_HASHPOLICY_FILTERSTATE']._serialized_start=10425
  _globals['_ROUTEACTION_HASHPOLICY_FILTERSTATE']._serialized_end=10522
  _globals['_ROUTEACTION_UPGRADECONFIG']._serialized_start=10600
  _globals['_ROUTEACTION_UPGRADECONFIG']._serialized_end=10949
  _globals['_ROUTEACTION_UPGRADECONFIG_CONNECTCONFIG']._serialized_start=10787
  _globals['_ROUTEACTION_UPGRADECONFIG_CONNECTCONFIG']._serialized_end=10896
  _globals['_ROUTEACTION_MAXSTREAMDURATION']._serialized_start=10952
  _globals['_ROUTEACTION_MAXSTREAMDURATION']._serialized_end=11150
  _globals['_ROUTEACTION_CLUSTERNOTFOUNDRESPONSECODE']._serialized_start=11152
  _globals['_ROUTEACTION_CLUSTERNOTFOUNDRESPONSECODE']._serialized_end=11248
  _globals['_ROUTEACTION_INTERNALREDIRECTACTION']._serialized_start=11250
  _globals['_ROUTEACTION_INTERNALREDIRECTACTION']._serialized_end=11344
  _globals['_RETRYPOLICY']._serialized_start=11503
  _globals['_RETRYPOLICY']._serialized_end=13258
  _globals['_RETRYPOLICY_RETRYPRIORITY']._serialized_start=12338
  _globals['_RETRYPOLICY_RETRYPRIORITY']._serialized_end=12504
  _globals['_RETRYPOLICY_RETRYHOSTPREDICATE']._serialized_start=12507
  _globals['_RETRYPOLICY_RETRYHOSTPREDICATE']._serialized_end=12683
  _globals['_RETRYPOLICY_RETRYBACKOFF']._serialized_start=12686
  _globals['_RETRYPOLICY_RETRYBACKOFF']._serialized_end=12873
  _globals['_RETRYPOLICY_RESETHEADER']._serialized_start=12875
  _globals['_RETRYPOLICY_RESETHEADER']._serialized_end=12997
  _globals['_RETRYPOLICY_RATELIMITEDRETRYBACKOFF']._serialized_start=13000
  _globals['_RETRYPOLICY_RATELIMITEDRETRYBACKOFF']._serialized_end=13165
  _globals['_RETRYPOLICY_RESETHEADERFORMAT']._serialized_start=13167
  _globals['_RETRYPOLICY_RESETHEADERFORMAT']._serialized_end=13219
  _globals['_HEDGEPOLICY']._serialized_start=13261
  _globals['_HEDGEPOLICY']._serialized_end=13481
  _globals['_REDIRECTACTION']._serialized_start=13484
  _globals['_REDIRECTACTION']._serialized_end=14093
  _globals['_REDIRECTACTION_REDIRECTRESPONSECODE']._serialized_start=13878
  _globals['_REDIRECTACTION_REDIRECTRESPONSECODE']._serialized_end=13997
  _globals['_DIRECTRESPONSEACTION']._serialized_start=14096
  _globals['_DIRECTRESPONSEACTION']._serialized_end=14243
  _globals['_NONFORWARDINGACTION']._serialized_start=14245
  _globals['_NONFORWARDINGACTION']._serialized_end=14266
  _globals['_DECORATOR']._serialized_start=14268
  _globals['_DECORATOR']._serialized_end=14391
  _globals['_TRACING']._serialized_start=14394
  _globals['_TRACING']._serialized_end=14671
  _globals['_VIRTUALCLUSTER']._serialized_start=14674
  _globals['_VIRTUALCLUSTER']._serialized_end=14839
  _globals['_RATELIMIT']._serialized_start=14842
  _globals['_RATELIMIT']._serialized_end=18323
  _globals['_RATELIMIT_ACTION']._serialized_start=15153
  _globals['_RATELIMIT_ACTION']._serialized_end=17966
  _globals['_RATELIMIT_ACTION_SOURCECLUSTER']._serialized_start=16185
  _globals['_RATELIMIT_ACTION_SOURCECLUSTER']._serialized_end=16258
  _globals['_RATELIMIT_ACTION_DESTINATIONCLUSTER']._serialized_start=16260
  _globals['_RATELIMIT_ACTION_DESTINATIONCLUSTER']._serialized_end=16343
  _globals['_RATELIMIT_ACTION_REQUESTHEADERS']._serialized_start=16346
  _globals['_RATELIMIT_ACTION_REQUESTHEADERS']._serialized_end=16514
  _globals['_RATELIMIT_ACTION_QUERYPARAMETERS']._serialized_start=16516
  _globals['_RATELIMIT_ACTION_QUERYPARAMETERS']._serialized_end=16629
  _globals['_RATELIMIT_ACTION_REMOTEADDRESS']._serialized_start=16631
  _globals['_RATELIMIT_ACTION_REMOTEADDRESS']._serialized_end=16704
  _globals['_RATELIMIT_ACTION_MASKEDREMOTEADDRESS']._serialized_start=16707
  _globals['_RATELIMIT_ACTION_MASKEDREMOTEADDRESS']._serialized_end=16863
  _globals['_RATELIMIT_ACTION_GENERICKEY']._serialized_start=16865
  _globals['_RATELIMIT_ACTION_GENERICKEY']._serialized_end=16991
  _globals['_RATELIMIT_ACTION_HEADERVALUEMATCH']._serialized_start=16994
  _globals['_RATELIMIT_ACTION_HEADERVALUEMATCH']._serialized_end=17247
  _globals['_RATELIMIT_ACTION_DYNAMICMETADATA']._serialized_start=17250
  _globals['_RATELIMIT_ACTION_DYNAMICMETADATA']._serialized_end=17392
  _globals['_RATELIMIT_ACTION_METADATA']._serialized_start=17395
  _globals['_RATELIMIT_ACTION_METADATA']._serialized_end=17677
  _globals['_RATELIMIT_ACTION_METADATA_SOURCE']._serialized_start=17639
  _globals['_RATELIMIT_ACTION_METADATA_SOURCE']._serialized_end=17677
  _globals['_RATELIMIT_ACTION_QUERYPARAMETERVALUEMATCH']._serialized_start=17680
  _globals['_RATELIMIT_ACTION_QUERYPARAMETERVALUEMATCH']._serialized_end=17897
  _globals['_RATELIMIT_OVERRIDE']._serialized_start=17969
  _globals['_RATELIMIT_OVERRIDE']._serialized_end=18181
  _globals['_RATELIMIT_OVERRIDE_DYNAMICMETADATA']._serialized_start=18068
  _globals['_RATELIMIT_OVERRIDE_DYNAMICMETADATA']._serialized_end=18154
  _globals['_RATELIMIT_HITSADDEND']._serialized_start=18183
  _globals['_RATELIMIT_HITSADDEND']._serialized_end=18286
  _globals['_HEADERMATCHER']._serialized_start=18326
  _globals['_HEADERMATCHER']._serialized_end=18914
  _globals['_QUERYPARAMETERMATCHER']._serialized_start=18917
  _globals['_QUERYPARAMETERMATCHER']._serialized_end=19173
  _globals['_INTERNALREDIRECTPOLICY']._serialized_start=19176
  _globals['_INTERNALREDIRECTPOLICY']._serialized_end=19460
  _globals['_FILTERCONFIG']._serialized_start=19462
  _globals['_FILTERCONFIG']._serialized_end=19553
# @@protoc_insertion_point(module_scope)
