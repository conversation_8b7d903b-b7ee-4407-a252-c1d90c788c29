# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/listener/proxy_protocol/v2/proxy_protocol.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nCenvoy/config/filter/listener/proxy_protocol/v2/proxy_protocol.proto\x12.envoy.config.filter.listener.proxy_protocol.v2\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"\x0f\n\rProxyProtocolB\xff\x01\n<io.envoyproxy.envoy.config.filter.listener.proxy_protocol.v2B\x12ProxyProtocolProtoP\x01Zfgithub.com/envoyproxy/go-control-plane/envoy/config/filter/listener/proxy_protocol/v2;proxy_protocolv2\xf2\x98\xfe\x8f\x05\x35\x12\x33\x65nvoy.extensions.filters.listener.proxy_protocol.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.listener.proxy_protocol.v2.proxy_protocol_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n<io.envoyproxy.envoy.config.filter.listener.proxy_protocol.v2B\022ProxyProtocolProtoP\001Zfgithub.com/envoyproxy/go-control-plane/envoy/config/filter/listener/proxy_protocol/v2;proxy_protocolv2\362\230\376\217\0055\0223envoy.extensions.filters.listener.proxy_protocol.v3\272\200\310\321\006\002\020\001'
  _globals['_PROXYPROTOCOL']._serialized_start=182
  _globals['_PROXYPROTOCOL']._serialized_end=197
# @@protoc_insertion_point(module_scope)
