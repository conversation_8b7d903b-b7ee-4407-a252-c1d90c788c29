# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/zookeeper_proxy/v1alpha1/zookeeper_proxy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nJenvoy/config/filter/network/zookeeper_proxy/v1alpha1/zookeeper_proxy.proto\x12\x34\x65nvoy.config.filter.network.zookeeper_proxy.v1alpha1\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"z\n\x0eZooKeeperProxy\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x12\n\naccess_log\x18\x02 \x01(\t\x12\x36\n\x10max_packet_bytes\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\xfb\x01\nBio.envoyproxy.envoy.config.filter.network.zookeeper_proxy.v1alpha1B\x13ZookeeperProxyProtoP\x01Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/zookeeper_proxy/v1alpha1\xf2\x98\xfe\x8f\x05\x35\x12\x33\x65nvoy.extensions.filters.network.zookeeper_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.zookeeper_proxy.v1alpha1.zookeeper_proxy_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\nBio.envoyproxy.envoy.config.filter.network.zookeeper_proxy.v1alpha1B\023ZookeeperProxyProtoP\001Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/zookeeper_proxy/v1alpha1\362\230\376\217\0055\0223envoy.extensions.filters.network.zookeeper_proxy.v3\272\200\310\321\006\002\020\001'
  _ZOOKEEPERPROXY.fields_by_name['stat_prefix']._options = None
  _ZOOKEEPERPROXY.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _globals['_ZOOKEEPERPROXY']._serialized_start=252
  _globals['_ZOOKEEPERPROXY']._serialized_end=374
# @@protoc_insertion_point(module_scope)
