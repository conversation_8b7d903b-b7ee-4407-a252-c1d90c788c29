# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/core/v3/socket_option.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(envoy/config/core/v3/socket_option.proto\x12\x14\x65nvoy.config.core.v3\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\xa2\x04\n\x0cSocketOption\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\r\n\x05level\x18\x02 \x01(\x03\x12\x0c\n\x04name\x18\x03 \x01(\x03\x12\x13\n\tint_value\x18\x04 \x01(\x03H\x00\x12\x13\n\tbuf_value\x18\x05 \x01(\x0cH\x00\x12G\n\x05state\x18\x06 \x01(\x0e\x32..envoy.config.core.v3.SocketOption.SocketStateB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12;\n\x04type\x18\x07 \x01(\x0b\x32-.envoy.config.core.v3.SocketOption.SocketType\x1a\xb2\x01\n\nSocketType\x12\x44\n\x06stream\x18\x01 \x01(\x0b\x32\x34.envoy.config.core.v3.SocketOption.SocketType.Stream\x12H\n\x08\x64\x61tagram\x18\x02 \x01(\x0b\x32\x36.envoy.config.core.v3.SocketOption.SocketType.Datagram\x1a\x08\n\x06Stream\x1a\n\n\x08\x44\x61tagram\"F\n\x0bSocketState\x12\x11\n\rSTATE_PREBIND\x10\x00\x12\x0f\n\x0bSTATE_BOUND\x10\x01\x12\x13\n\x0fSTATE_LISTENING\x10\x02:%\x9a\xc5\x88\x1e \n\x1e\x65nvoy.api.v2.core.SocketOptionB\x0c\n\x05value\x12\x03\xf8\x42\x01\"S\n\x15SocketOptionsOverride\x12:\n\x0esocket_options\x18\x01 \x03(\x0b\x32\".envoy.config.core.v3.SocketOptionB\x85\x01\n\"io.envoyproxy.envoy.config.core.v3B\x11SocketOptionProtoP\x01ZBgithub.com/envoyproxy/go-control-plane/envoy/config/core/v3;corev3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.core.v3.socket_option_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\"io.envoyproxy.envoy.config.core.v3B\021SocketOptionProtoP\001ZBgithub.com/envoyproxy/go-control-plane/envoy/config/core/v3;corev3\272\200\310\321\006\002\020\002'
  _SOCKETOPTION.oneofs_by_name['value']._options = None
  _SOCKETOPTION.oneofs_by_name['value']._serialized_options = b'\370B\001'
  _SOCKETOPTION.fields_by_name['state']._options = None
  _SOCKETOPTION.fields_by_name['state']._serialized_options = b'\372B\005\202\001\002\020\001'
  _SOCKETOPTION._options = None
  _SOCKETOPTION._serialized_options = b'\232\305\210\036 \n\036envoy.api.v2.core.SocketOption'
  _globals['_SOCKETOPTION']._serialized_start=158
  _globals['_SOCKETOPTION']._serialized_end=704
  _globals['_SOCKETOPTION_SOCKETTYPE']._serialized_start=401
  _globals['_SOCKETOPTION_SOCKETTYPE']._serialized_end=579
  _globals['_SOCKETOPTION_SOCKETTYPE_STREAM']._serialized_start=559
  _globals['_SOCKETOPTION_SOCKETTYPE_STREAM']._serialized_end=567
  _globals['_SOCKETOPTION_SOCKETTYPE_DATAGRAM']._serialized_start=569
  _globals['_SOCKETOPTION_SOCKETTYPE_DATAGRAM']._serialized_end=579
  _globals['_SOCKETOPTION_SOCKETSTATE']._serialized_start=581
  _globals['_SOCKETOPTION_SOCKETSTATE']._serialized_end=651
  _globals['_SOCKETOPTIONSOVERRIDE']._serialized_start=706
  _globals['_SOCKETOPTIONSOVERRIDE']._serialized_end=789
# @@protoc_insertion_point(module_scope)
