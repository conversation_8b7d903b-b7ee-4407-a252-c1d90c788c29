# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/thrift/router/v2alpha1/router.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7envoy/config/filter/thrift/router/v2alpha1/router.proto\x12*envoy.config.filter.thrift.router.v2alpha1\x1a\x1dudpa/annotations/status.proto\"\x08\n\x06RouterB\xa4\x01\n8io.envoyproxy.envoy.config.filter.thrift.router.v2alpha1B\x0bRouterProtoP\x01ZQgithub.com/envoyproxy/go-control-plane/envoy/config/filter/thrift/router/v2alpha1\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.thrift.router.v2alpha1.router_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n8io.envoyproxy.envoy.config.filter.thrift.router.v2alpha1B\013RouterProtoP\001ZQgithub.com/envoyproxy/go-control-plane/envoy/config/filter/thrift/router/v2alpha1\272\200\310\321\006\002\020\002'
  _globals['_ROUTER']._serialized_start=134
  _globals['_ROUTER']._serialized_end=142
# @@protoc_insertion_point(module_scope)
