# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/ratelimit/v3/rls.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.core.v3 import config_source_pb2 as envoy_dot_config_dot_core_dot_v3_dot_config__source__pb2
from envoy.config.core.v3 import grpc_service_pb2 as envoy_dot_config_dot_core_dot_v3_dot_grpc__service__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#envoy/config/ratelimit/v3/rls.proto\x12\x19\x65nvoy.config.ratelimit.v3\x1a(envoy/config/core/v3/config_source.proto\x1a\'envoy/config/core/v3/grpc_service.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\xeb\x01\n\x16RateLimitServiceConfig\x12\x41\n\x0cgrpc_service\x18\x02 \x01(\x0b\x32!.envoy.config.core.v3.GrpcServiceB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12I\n\x15transport_api_version\x18\x04 \x01(\x0e\x32 .envoy.config.core.v3.ApiVersionB\x08\xfa\x42\x05\x82\x01\x02\x10\x01:7\x9a\xc5\x88\x1e\x32\n0envoy.config.ratelimit.v2.RateLimitServiceConfigJ\x04\x08\x01\x10\x02J\x04\x08\x03\x10\x04\x42\x8b\x01\n\'io.envoyproxy.envoy.config.ratelimit.v3B\x08RlsProtoP\x01ZLgithub.com/envoyproxy/go-control-plane/envoy/config/ratelimit/v3;ratelimitv3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.ratelimit.v3.rls_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\'io.envoyproxy.envoy.config.ratelimit.v3B\010RlsProtoP\001ZLgithub.com/envoyproxy/go-control-plane/envoy/config/ratelimit/v3;ratelimitv3\272\200\310\321\006\002\020\002'
  _RATELIMITSERVICECONFIG.fields_by_name['grpc_service']._options = None
  _RATELIMITSERVICECONFIG.fields_by_name['grpc_service']._serialized_options = b'\372B\005\212\001\002\020\001'
  _RATELIMITSERVICECONFIG.fields_by_name['transport_api_version']._options = None
  _RATELIMITSERVICECONFIG.fields_by_name['transport_api_version']._serialized_options = b'\372B\005\202\001\002\020\001'
  _RATELIMITSERVICECONFIG._options = None
  _RATELIMITSERVICECONFIG._serialized_options = b'\232\305\210\0362\n0envoy.config.ratelimit.v2.RateLimitServiceConfig'
  _globals['_RATELIMITSERVICECONFIG']._serialized_start=241
  _globals['_RATELIMITSERVICECONFIG']._serialized_end=476
# @@protoc_insertion_point(module_scope)
