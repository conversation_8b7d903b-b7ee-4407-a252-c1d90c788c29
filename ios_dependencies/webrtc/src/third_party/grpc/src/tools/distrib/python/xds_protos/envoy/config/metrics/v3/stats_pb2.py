# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/metrics/v3/stats.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.core.v3 import address_pb2 as envoy_dot_config_dot_core_dot_v3_dot_address__pb2
from envoy.type.matcher.v3 import string_pb2 as envoy_dot_type_dot_matcher_dot_v3_dot_string__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#envoy/config/metrics/v3/stats.proto\x12\x17\x65nvoy.config.metrics.v3\x1a\"envoy/config/core/v3/address.proto\x1a\"envoy/type/matcher/v3/string.proto\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\x8e\x01\n\tStatsSink\x12\x0c\n\x04name\x18\x01 \x01(\t\x12,\n\x0ctyped_config\x18\x03 \x01(\x0b\x32\x14.google.protobuf.AnyH\x00:(\x9a\xc5\x88\x1e#\n!envoy.config.metrics.v2.StatsSinkB\r\n\x0b\x63onfig_typeJ\x04\x08\x02\x10\x03R\x06\x63onfig\"\xc1\x02\n\x0bStatsConfig\x12\x39\n\nstats_tags\x18\x01 \x03(\x0b\x32%.envoy.config.metrics.v3.TagSpecifier\x12\x38\n\x14use_all_default_tags\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12<\n\rstats_matcher\x18\x03 \x01(\x0b\x32%.envoy.config.metrics.v3.StatsMatcher\x12S\n\x19histogram_bucket_settings\x18\x04 \x03(\x0b\x32\x30.envoy.config.metrics.v3.HistogramBucketSettings:*\x9a\xc5\x88\x1e%\n#envoy.config.metrics.v2.StatsConfig\"\xef\x01\n\x0cStatsMatcher\x12\x14\n\nreject_all\x18\x01 \x01(\x08H\x00\x12\x42\n\x0e\x65xclusion_list\x18\x02 \x01(\x0b\x32(.envoy.type.matcher.v3.ListStringMatcherH\x00\x12\x42\n\x0einclusion_list\x18\x03 \x01(\x0b\x32(.envoy.type.matcher.v3.ListStringMatcherH\x00:+\x9a\xc5\x88\x1e&\n$envoy.config.metrics.v2.StatsMatcherB\x14\n\rstats_matcher\x12\x03\xf8\x42\x01\"\x8c\x01\n\x0cTagSpecifier\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x19\n\x05regex\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03(\x80\x08H\x00\x12\x15\n\x0b\x66ixed_value\x18\x03 \x01(\tH\x00:+\x9a\xc5\x88\x1e&\n$envoy.config.metrics.v2.TagSpecifierB\x0b\n\ttag_value\"\x82\x01\n\x17HistogramBucketSettings\x12=\n\x05match\x18\x01 \x01(\x0b\x32$.envoy.type.matcher.v3.StringMatcherB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12(\n\x07\x62uckets\x18\x02 \x03(\x01\x42\x17\xfa\x42\x14\x92\x01\x11\x08\x01\x18\x01\"\x0b\x12\t!\x00\x00\x00\x00\x00\x00\x00\x00\"\xae\x01\n\nStatsdSink\x12\x30\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32\x1d.envoy.config.core.v3.AddressH\x00\x12\x1a\n\x10tcp_cluster_name\x18\x02 \x01(\tH\x00\x12\x0e\n\x06prefix\x18\x03 \x01(\t:)\x9a\xc5\x88\x1e$\n\"envoy.config.metrics.v2.StatsdSinkB\x17\n\x10statsd_specifier\x12\x03\xf8\x42\x01\"\xe9\x01\n\rDogStatsdSink\x12\x30\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32\x1d.envoy.config.core.v3.AddressH\x00\x12\x0e\n\x06prefix\x18\x03 \x01(\t\x12\x45\n\x16max_bytes_per_datagram\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.UInt64ValueB\x07\xfa\x42\x04\x32\x02 \x00:,\x9a\xc5\x88\x1e\'\n%envoy.config.metrics.v2.DogStatsdSinkB\x1b\n\x14\x64og_statsd_specifier\x12\x03\xf8\x42\x01J\x04\x08\x02\x10\x03\"N\n\x0bHystrixSink\x12\x13\n\x0bnum_buckets\x18\x01 \x01(\x03:*\x9a\xc5\x88\x1e%\n#envoy.config.metrics.v2.HystrixSinkB\x87\x01\n%io.envoyproxy.envoy.config.metrics.v3B\nStatsProtoP\x01ZHgithub.com/envoyproxy/go-control-plane/envoy/config/metrics/v3;metricsv3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.metrics.v3.stats_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n%io.envoyproxy.envoy.config.metrics.v3B\nStatsProtoP\001ZHgithub.com/envoyproxy/go-control-plane/envoy/config/metrics/v3;metricsv3\272\200\310\321\006\002\020\002'
  _STATSSINK._options = None
  _STATSSINK._serialized_options = b'\232\305\210\036#\n!envoy.config.metrics.v2.StatsSink'
  _STATSCONFIG._options = None
  _STATSCONFIG._serialized_options = b'\232\305\210\036%\n#envoy.config.metrics.v2.StatsConfig'
  _STATSMATCHER.oneofs_by_name['stats_matcher']._options = None
  _STATSMATCHER.oneofs_by_name['stats_matcher']._serialized_options = b'\370B\001'
  _STATSMATCHER._options = None
  _STATSMATCHER._serialized_options = b'\232\305\210\036&\n$envoy.config.metrics.v2.StatsMatcher'
  _TAGSPECIFIER.fields_by_name['regex']._options = None
  _TAGSPECIFIER.fields_by_name['regex']._serialized_options = b'\372B\005r\003(\200\010'
  _TAGSPECIFIER._options = None
  _TAGSPECIFIER._serialized_options = b'\232\305\210\036&\n$envoy.config.metrics.v2.TagSpecifier'
  _HISTOGRAMBUCKETSETTINGS.fields_by_name['match']._options = None
  _HISTOGRAMBUCKETSETTINGS.fields_by_name['match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _HISTOGRAMBUCKETSETTINGS.fields_by_name['buckets']._options = None
  _HISTOGRAMBUCKETSETTINGS.fields_by_name['buckets']._serialized_options = b'\372B\024\222\001\021\010\001\030\001\"\013\022\t!\000\000\000\000\000\000\000\000'
  _STATSDSINK.oneofs_by_name['statsd_specifier']._options = None
  _STATSDSINK.oneofs_by_name['statsd_specifier']._serialized_options = b'\370B\001'
  _STATSDSINK._options = None
  _STATSDSINK._serialized_options = b'\232\305\210\036$\n\"envoy.config.metrics.v2.StatsdSink'
  _DOGSTATSDSINK.oneofs_by_name['dog_statsd_specifier']._options = None
  _DOGSTATSDSINK.oneofs_by_name['dog_statsd_specifier']._serialized_options = b'\370B\001'
  _DOGSTATSDSINK.fields_by_name['max_bytes_per_datagram']._options = None
  _DOGSTATSDSINK.fields_by_name['max_bytes_per_datagram']._serialized_options = b'\372B\0042\002 \000'
  _DOGSTATSDSINK._options = None
  _DOGSTATSDSINK._serialized_options = b'\232\305\210\036\'\n%envoy.config.metrics.v2.DogStatsdSink'
  _HYSTRIXSINK._options = None
  _HYSTRIXSINK._serialized_options = b'\232\305\210\036%\n#envoy.config.metrics.v2.HystrixSink'
  _globals['_STATSSINK']._serialized_start=287
  _globals['_STATSSINK']._serialized_end=429
  _globals['_STATSCONFIG']._serialized_start=432
  _globals['_STATSCONFIG']._serialized_end=753
  _globals['_STATSMATCHER']._serialized_start=756
  _globals['_STATSMATCHER']._serialized_end=995
  _globals['_TAGSPECIFIER']._serialized_start=998
  _globals['_TAGSPECIFIER']._serialized_end=1138
  _globals['_HISTOGRAMBUCKETSETTINGS']._serialized_start=1141
  _globals['_HISTOGRAMBUCKETSETTINGS']._serialized_end=1271
  _globals['_STATSDSINK']._serialized_start=1274
  _globals['_STATSDSINK']._serialized_end=1448
  _globals['_DOGSTATSDSINK']._serialized_start=1451
  _globals['_DOGSTATSDSINK']._serialized_end=1684
  _globals['_HYSTRIXSINK']._serialized_start=1686
  _globals['_HYSTRIXSINK']._serialized_end=1764
# @@protoc_insertion_point(module_scope)
