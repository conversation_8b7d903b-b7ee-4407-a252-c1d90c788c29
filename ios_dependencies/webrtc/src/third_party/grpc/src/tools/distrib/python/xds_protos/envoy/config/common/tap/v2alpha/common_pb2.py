# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/common/tap/v2alpha/common.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.service.tap.v2alpha import common_pb2 as envoy_dot_service_dot_tap_dot_v2alpha_dot_common__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,envoy/config/common/tap/v2alpha/common.proto\x12\x1f\x65nvoy.config.common.tap.v2alpha\x1a&envoy/service/tap/v2alpha/common.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xb0\x01\n\x15\x43ommonExtensionConfig\x12\x44\n\x0c\x61\x64min_config\x18\x01 \x01(\x0b\x32,.envoy.config.common.tap.v2alpha.AdminConfigH\x00\x12=\n\rstatic_config\x18\x02 \x01(\x0b\x32$.envoy.service.tap.v2alpha.TapConfigH\x00\x42\x12\n\x0b\x63onfig_type\x12\x03\xf8\x42\x01\")\n\x0b\x41\x64minConfig\x12\x1a\n\tconfig_id\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x42\xb4\x01\n-io.envoyproxy.envoy.config.common.tap.v2alphaB\x0b\x43ommonProtoP\x01ZFgithub.com/envoyproxy/go-control-plane/envoy/config/common/tap/v2alpha\xf2\x98\xfe\x8f\x05 \x12\x1e\x65nvoy.extensions.common.tap.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.common.tap.v2alpha.common_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n-io.envoyproxy.envoy.config.common.tap.v2alphaB\013CommonProtoP\001ZFgithub.com/envoyproxy/go-control-plane/envoy/config/common/tap/v2alpha\362\230\376\217\005 \022\036envoy.extensions.common.tap.v3\272\200\310\321\006\002\020\001'
  _COMMONEXTENSIONCONFIG.oneofs_by_name['config_type']._options = None
  _COMMONEXTENSIONCONFIG.oneofs_by_name['config_type']._serialized_options = b'\370B\001'
  _ADMINCONFIG.fields_by_name['config_id']._options = None
  _ADMINCONFIG.fields_by_name['config_id']._serialized_options = b'\372B\004r\002 \001'
  _globals['_COMMONEXTENSIONCONFIG']._serialized_start=210
  _globals['_COMMONEXTENSIONCONFIG']._serialized_end=386
  _globals['_ADMINCONFIG']._serialized_start=388
  _globals['_ADMINCONFIG']._serialized_end=429
# @@protoc_insertion_point(module_scope)
