# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/sni_cluster/v2/sni_cluster.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n<envoy/config/filter/network/sni_cluster/v2/sni_cluster.proto\x12*envoy.config.filter.network.sni_cluster.v2\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"\x0c\n\nSniClusterB\xed\x01\n8io.envoyproxy.envoy.config.filter.network.sni_cluster.v2B\x0fSniClusterProtoP\x01Z_github.com/envoyproxy/go-control-plane/envoy/config/filter/network/sni_cluster/v2;sni_clusterv2\xf2\x98\xfe\x8f\x05\x31\x12/envoy.extensions.filters.network.sni_cluster.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.sni_cluster.v2.sni_cluster_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n8io.envoyproxy.envoy.config.filter.network.sni_cluster.v2B\017SniClusterProtoP\001Z_github.com/envoyproxy/go-control-plane/envoy/config/filter/network/sni_cluster/v2;sni_clusterv2\362\230\376\217\0051\022/envoy.extensions.filters.network.sni_cluster.v3\272\200\310\321\006\002\020\001'
  _globals['_SNICLUSTER']._serialized_start=171
  _globals['_SNICLUSTER']._serialized_end=183
# @@protoc_insertion_point(module_scope)
