# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/adaptive_concurrency/v2alpha/adaptive_concurrency.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.type import percent_pb2 as envoy_dot_type_dot_percent__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nPenvoy/config/filter/http/adaptive_concurrency/v2alpha/adaptive_concurrency.proto\x12\x35\x65nvoy.config.filter.http.adaptive_concurrency.v2alpha\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a\x18\x65nvoy/type/percent.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xe2\x06\n\x18GradientControllerConfig\x12\x38\n\x1bsample_aggregate_percentile\x18\x01 \x01(\x0b\x32\x13.envoy.type.Percent\x12\x9d\x01\n\x18\x63oncurrency_limit_params\x18\x02 \x01(\x0b\x32q.envoy.config.filter.http.adaptive_concurrency.v2alpha.GradientControllerConfig.ConcurrencyLimitCalculationParamsB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x92\x01\n\x13min_rtt_calc_params\x18\x03 \x01(\x0b\x32k.envoy.config.filter.http.adaptive_concurrency.v2alpha.GradientControllerConfig.MinimumRTTCalculationParamsB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x1a\xb5\x01\n!ConcurrencyLimitCalculationParams\x12\x44\n\x15max_concurrency_limit\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02 \x00\x12J\n\x1b\x63oncurrency_update_interval\x18\x03 \x01(\x0b\x32\x19.google.protobuf.DurationB\n\xfa\x42\x07\xaa\x01\x04\x08\x01*\x00\x1a\x9e\x02\n\x1bMinimumRTTCalculationParams\x12\x37\n\x08interval\x18\x01 \x01(\x0b\x32\x19.google.protobuf.DurationB\n\xfa\x42\x07\xaa\x01\x04\x08\x01*\x00\x12<\n\rrequest_count\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02 \x00\x12#\n\x06jitter\x18\x03 \x01(\x0b\x32\x13.envoy.type.Percent\x12>\n\x0fmin_concurrency\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x07\xfa\x42\x04*\x02 \x00\x12#\n\x06\x62uffer\x18\x05 \x01(\x0b\x32\x13.envoy.type.Percent\"\xf4\x01\n\x13\x41\x64\x61ptiveConcurrency\x12\x7f\n\x1agradient_controller_config\x18\x01 \x01(\x0b\x32O.envoy.config.filter.http.adaptive_concurrency.v2alpha.GradientControllerConfigB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01H\x00\x12\x36\n\x07\x65nabled\x18\x02 \x01(\x0b\x32%.envoy.api.v2.core.RuntimeFeatureFlagB$\n\x1d\x63oncurrency_controller_config\x12\x03\xf8\x42\x01\x42\x84\x02\nCio.envoyproxy.envoy.config.filter.http.adaptive_concurrency.v2alphaB\x18\x41\x64\x61ptiveConcurrencyProtoP\x01Z\\github.com/envoyproxy/go-control-plane/envoy/config/filter/http/adaptive_concurrency/v2alpha\xf2\x98\xfe\x8f\x05\x37\x12\x35\x65nvoy.extensions.filters.http.adaptive_concurrency.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.adaptive_concurrency.v2alpha.adaptive_concurrency_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\nCio.envoyproxy.envoy.config.filter.http.adaptive_concurrency.v2alphaB\030AdaptiveConcurrencyProtoP\001Z\\github.com/envoyproxy/go-control-plane/envoy/config/filter/http/adaptive_concurrency/v2alpha\362\230\376\217\0057\0225envoy.extensions.filters.http.adaptive_concurrency.v3\272\200\310\321\006\002\020\001'
  _GRADIENTCONTROLLERCONFIG_CONCURRENCYLIMITCALCULATIONPARAMS.fields_by_name['max_concurrency_limit']._options = None
  _GRADIENTCONTROLLERCONFIG_CONCURRENCYLIMITCALCULATIONPARAMS.fields_by_name['max_concurrency_limit']._serialized_options = b'\372B\004*\002 \000'
  _GRADIENTCONTROLLERCONFIG_CONCURRENCYLIMITCALCULATIONPARAMS.fields_by_name['concurrency_update_interval']._options = None
  _GRADIENTCONTROLLERCONFIG_CONCURRENCYLIMITCALCULATIONPARAMS.fields_by_name['concurrency_update_interval']._serialized_options = b'\372B\007\252\001\004\010\001*\000'
  _GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS.fields_by_name['interval']._options = None
  _GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS.fields_by_name['interval']._serialized_options = b'\372B\007\252\001\004\010\001*\000'
  _GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS.fields_by_name['request_count']._options = None
  _GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS.fields_by_name['request_count']._serialized_options = b'\372B\004*\002 \000'
  _GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS.fields_by_name['min_concurrency']._options = None
  _GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS.fields_by_name['min_concurrency']._serialized_options = b'\372B\004*\002 \000'
  _GRADIENTCONTROLLERCONFIG.fields_by_name['concurrency_limit_params']._options = None
  _GRADIENTCONTROLLERCONFIG.fields_by_name['concurrency_limit_params']._serialized_options = b'\372B\005\212\001\002\020\001'
  _GRADIENTCONTROLLERCONFIG.fields_by_name['min_rtt_calc_params']._options = None
  _GRADIENTCONTROLLERCONFIG.fields_by_name['min_rtt_calc_params']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ADAPTIVECONCURRENCY.oneofs_by_name['concurrency_controller_config']._options = None
  _ADAPTIVECONCURRENCY.oneofs_by_name['concurrency_controller_config']._serialized_options = b'\370B\001'
  _ADAPTIVECONCURRENCY.fields_by_name['gradient_controller_config']._options = None
  _ADAPTIVECONCURRENCY.fields_by_name['gradient_controller_config']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GRADIENTCONTROLLERCONFIG']._serialized_start=348
  _globals['_GRADIENTCONTROLLERCONFIG']._serialized_end=1214
  _globals['_GRADIENTCONTROLLERCONFIG_CONCURRENCYLIMITCALCULATIONPARAMS']._serialized_start=744
  _globals['_GRADIENTCONTROLLERCONFIG_CONCURRENCYLIMITCALCULATIONPARAMS']._serialized_end=925
  _globals['_GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS']._serialized_start=928
  _globals['_GRADIENTCONTROLLERCONFIG_MINIMUMRTTCALCULATIONPARAMS']._serialized_end=1214
  _globals['_ADAPTIVECONCURRENCY']._serialized_start=1217
  _globals['_ADAPTIVECONCURRENCY']._serialized_end=1461
# @@protoc_insertion_point(module_scope)
