# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/listener/original_src/v2alpha1/original_src.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nEenvoy/config/filter/listener/original_src/v2alpha1/original_src.proto\x12\x32\x65nvoy.config.filter.listener.original_src.v2alpha1\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\".\n\x0bOriginalSrc\x12\x11\n\tbind_port\x18\x01 \x01(\x08\x12\x0c\n\x04mark\x18\x02 \x01(\rB\xf2\x01\<EMAIL>.original_src.v2alpha1B\x10OriginalSrcProtoP\x01ZYgithub.com/envoyproxy/go-control-plane/envoy/config/filter/listener/original_src/v2alpha1\xf2\x98\xfe\x8f\x05\x33\x12\x31\x65nvoy.extensions.filters.listener.original_src.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.listener.original_src.v2alpha1.original_src_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\<EMAIL>.original_src.v2alpha1B\020OriginalSrcProtoP\001ZYgithub.com/envoyproxy/go-control-plane/envoy/config/filter/listener/original_src/v2alpha1\362\230\376\217\0053\0221envoy.extensions.filters.listener.original_src.v3\272\200\310\321\006\002\020\001'
  _globals['_ORIGINALSRC']._serialized_start=188
  _globals['_ORIGINALSRC']._serialized_end=234
# @@protoc_insertion_point(module_scope)
