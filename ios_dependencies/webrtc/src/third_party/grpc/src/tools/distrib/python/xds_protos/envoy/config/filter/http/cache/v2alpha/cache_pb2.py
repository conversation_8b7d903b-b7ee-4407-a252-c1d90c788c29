# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/cache/v2alpha/cache.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from envoy.type.matcher import string_pb2 as envoy_dot_type_dot_matcher_dot_string__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2envoy/config/filter/http/cache/v2alpha/cache.proto\x12&envoy.config.filter.http.cache.v2alpha\x1a)envoy/api/v2/route/route_components.proto\x1a\x1f\x65nvoy/type/matcher/string.proto\x1a\x19google/protobuf/any.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xdd\x03\n\x0b\x43\x61\x63heConfig\x12\x34\n\x0ctyped_config\x18\x01 \x01(\x0b\x32\x14.google.protobuf.AnyB\x08\xfa\x42\x05\xa2\x01\x02\x08\x01\x12?\n\x14\x61llowed_vary_headers\x18\x02 \x03(\x0b\x32!.envoy.type.matcher.StringMatcher\x12`\n\x12key_creator_params\x18\x03 \x01(\x0b\x32\x44.envoy.config.filter.http.cache.v2alpha.CacheConfig.KeyCreatorParams\x12\x16\n\x0emax_body_bytes\x18\x04 \x01(\r\x1a\xdc\x01\n\x10KeyCreatorParams\x12\x16\n\x0e\x65xclude_scheme\x18\x01 \x01(\x08\x12\x14\n\x0c\x65xclude_host\x18\x02 \x01(\x08\x12L\n\x19query_parameters_included\x18\x03 \x03(\x0b\x32).envoy.api.v2.route.QueryParameterMatcher\x12L\n\x19query_parameters_excluded\x18\x04 \x03(\x0b\x32).envoy.api.v2.route.QueryParameterMatcherB\xd0\x01\n4io.envoyproxy.envoy.config.filter.http.cache.v2alphaB\nCacheProtoP\x01ZMgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/cache/v2alpha\xf2\x98\xfe\x8f\x05-\x12+envoy.extensions.filters.http.cache.v3alpha\xba\x80\xc8\xd1\x06\x04\x08\x01\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.cache.v2alpha.cache_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n4io.envoyproxy.envoy.config.filter.http.cache.v2alphaB\nCacheProtoP\001ZMgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/cache/v2alpha\362\230\376\217\005-\022+envoy.extensions.filters.http.cache.v3alpha\272\200\310\321\006\004\010\001\020\001'
  _CACHECONFIG.fields_by_name['typed_config']._options = None
  _CACHECONFIG.fields_by_name['typed_config']._serialized_options = b'\372B\005\242\001\002\010\001'
  _globals['_CACHECONFIG']._serialized_start=286
  _globals['_CACHECONFIG']._serialized_end=763
  _globals['_CACHECONFIG_KEYCREATORPARAMS']._serialized_start=543
  _globals['_CACHECONFIG_KEYCREATORPARAMS']._serialized_end=763
# @@protoc_insertion_point(module_scope)
