# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/gzip/v2/gzip.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.filter.http.compressor.v2 import compressor_pb2 as envoy_dot_config_dot_filter_dot_http_dot_compressor_dot_v2_dot_compressor__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+envoy/config/filter/http/gzip/v2/gzip.proto\x12 envoy.config.filter.http.gzip.v2\x1a\x37\x65nvoy/config/filter/http/compressor/v2/compressor.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xbb\x05\n\x04Gzip\x12=\n\x0cmemory_level\x18\x01 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\t\xfa\x42\x06*\x04\x18\t(\x01\x12\x38\n\x0e\x63ontent_length\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\x02\x18\x01\x12\x61\n\x11\x63ompression_level\x18\x03 \x01(\x0e\x32<.envoy.config.filter.http.gzip.v2.Gzip.CompressionLevel.EnumB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x62\n\x14\x63ompression_strategy\x18\x04 \x01(\x0e\x32:.envoy.config.filter.http.gzip.v2.Gzip.CompressionStrategyB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\x12\x18\n\x0c\x63ontent_type\x18\x06 \x03(\tB\x02\x18\x01\x12\"\n\x16\x64isable_on_etag_header\x18\x07 \x01(\x08\x42\x02\x18\x01\x12)\n\x1dremove_accept_encoding_header\x18\x08 \x01(\x08\x42\x02\x18\x01\x12<\n\x0bwindow_bits\x18\t \x01(\x0b\x32\x1c.google.protobuf.UInt32ValueB\t\xfa\x42\x06*\x04\x18\x0f(\t\x12\x46\n\ncompressor\x18\n \x01(\x0b\x32\x32.envoy.config.filter.http.compressor.v2.Compressor\x1a<\n\x10\x43ompressionLevel\"(\n\x04\x45num\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x08\n\x04\x42\x45ST\x10\x01\x12\t\n\x05SPEED\x10\x02\"F\n\x13\x43ompressionStrategy\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x0c\n\x08\x46ILTERED\x10\x01\x12\x0b\n\x07HUFFMAN\x10\x02\x12\x07\n\x03RLE\x10\x03\x42\xc2\x01\n.io.envoyproxy.envoy.config.filter.http.gzip.v2B\tGzipProtoP\x01ZNgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/gzip/v2;gzipv2\xf2\x98\xfe\x8f\x05\'\x12%envoy.extensions.filters.http.gzip.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.gzip.v2.gzip_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n.io.envoyproxy.envoy.config.filter.http.gzip.v2B\tGzipProtoP\001ZNgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/gzip/v2;gzipv2\362\230\376\217\005\'\022%envoy.extensions.filters.http.gzip.v3\272\200\310\321\006\002\020\001'
  _GZIP.fields_by_name['memory_level']._options = None
  _GZIP.fields_by_name['memory_level']._serialized_options = b'\372B\006*\004\030\t(\001'
  _GZIP.fields_by_name['content_length']._options = None
  _GZIP.fields_by_name['content_length']._serialized_options = b'\030\001'
  _GZIP.fields_by_name['compression_level']._options = None
  _GZIP.fields_by_name['compression_level']._serialized_options = b'\372B\005\202\001\002\020\001'
  _GZIP.fields_by_name['compression_strategy']._options = None
  _GZIP.fields_by_name['compression_strategy']._serialized_options = b'\372B\005\202\001\002\020\001'
  _GZIP.fields_by_name['content_type']._options = None
  _GZIP.fields_by_name['content_type']._serialized_options = b'\030\001'
  _GZIP.fields_by_name['disable_on_etag_header']._options = None
  _GZIP.fields_by_name['disable_on_etag_header']._serialized_options = b'\030\001'
  _GZIP.fields_by_name['remove_accept_encoding_header']._options = None
  _GZIP.fields_by_name['remove_accept_encoding_header']._serialized_options = b'\030\001'
  _GZIP.fields_by_name['window_bits']._options = None
  _GZIP.fields_by_name['window_bits']._serialized_options = b'\372B\006*\004\030\017(\t'
  _globals['_GZIP']._serialized_start=259
  _globals['_GZIP']._serialized_end=958
  _globals['_GZIP_COMPRESSIONLEVEL']._serialized_start=826
  _globals['_GZIP_COMPRESSIONLEVEL']._serialized_end=886
  _globals['_GZIP_COMPRESSIONLEVEL_ENUM']._serialized_start=846
  _globals['_GZIP_COMPRESSIONLEVEL_ENUM']._serialized_end=886
  _globals['_GZIP_COMPRESSIONSTRATEGY']._serialized_start=888
  _globals['_GZIP_COMPRESSIONSTRATEGY']._serialized_end=958
# @@protoc_insertion_point(module_scope)
