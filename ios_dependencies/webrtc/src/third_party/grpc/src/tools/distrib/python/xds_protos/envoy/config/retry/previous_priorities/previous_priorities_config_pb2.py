# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/retry/previous_priorities/previous_priorities_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nGenvoy/config/retry/previous_priorities/previous_priorities_config.proto\x12&envoy.config.retry.previous_priorities\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"=\n\x18PreviousPrioritiesConfig\x12!\n\x10update_frequency\x18\x01 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00\x42\xec\x01\n4io.envoyproxy.envoy.config.retry.previous_prioritiesB\x1dPreviousPrioritiesConfigProtoP\x01ZMgithub.com/envoyproxy/go-control-plane/envoy/config/retry/previous_priorities\xf2\x98\xfe\x8f\x05\x38\x12\x36\x65nvoy.extensions.retry.priority.previous_priorities.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.retry.previous_priorities.previous_priorities_config_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n4io.envoyproxy.envoy.config.retry.previous_prioritiesB\035PreviousPrioritiesConfigProtoP\001ZMgithub.com/envoyproxy/go-control-plane/envoy/config/retry/previous_priorities\362\230\376\217\0058\0226envoy.extensions.retry.priority.previous_priorities.v3\272\200\310\321\006\002\020\001'
  _PREVIOUSPRIORITIESCONFIG.fields_by_name['update_frequency']._options = None
  _PREVIOUSPRIORITIESCONFIG.fields_by_name['update_frequency']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_PREVIOUSPRIORITIESCONFIG']._serialized_start=203
  _globals['_PREVIOUSPRIORITIESCONFIG']._serialized_end=264
# @@protoc_insertion_point(module_scope)
