# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/health_checker/redis/v2/redis.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0envoy/config/health_checker/redis/v2/redis.proto\x12$envoy.config.health_checker.redis.v2\x1a\x1dudpa/annotations/status.proto\"\x14\n\x05Redis\x12\x0b\n\x03key\x18\x01 \x01(\tB\x9f\x01\n2io.envoyproxy.envoy.config.health_checker.redis.v2B\nRedisProtoP\x01ZSgithub.com/envoyproxy/go-control-plane/envoy/config/health_checker/redis/v2;redisv2\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.health_checker.redis.v2.redis_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n2io.envoyproxy.envoy.config.health_checker.redis.v2B\nRedisProtoP\001ZSgithub.com/envoyproxy/go-control-plane/envoy/config/health_checker/redis/v2;redisv2\272\200\310\321\006\002\020\002'
  _globals['_REDIS']._serialized_start=121
  _globals['_REDIS']._serialized_end=141
# @@protoc_insertion_point(module_scope)
