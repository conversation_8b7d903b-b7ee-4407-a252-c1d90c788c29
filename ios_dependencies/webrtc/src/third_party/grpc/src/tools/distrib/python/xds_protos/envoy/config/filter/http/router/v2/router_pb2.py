# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/router/v2/router.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.filter.accesslog.v2 import accesslog_pb2 as envoy_dot_config_dot_filter_dot_accesslog_dot_v2_dot_accesslog__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/envoy/config/filter/http/router/v2/router.proto\x12\"envoy.config.filter.http.router.v2\x1a\x30\x65nvoy/config/filter/accesslog/v2/accesslog.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x93\x03\n\x06Router\x12\x31\n\rdynamic_stats\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12\x18\n\x10start_child_span\x18\x02 \x01(\x08\x12\x41\n\x0cupstream_log\x18\x03 \x03(\x0b\x32+.envoy.config.filter.accesslog.v2.AccessLog\x12\x1e\n\x16suppress_envoy_headers\x18\x04 \x01(\x08\x12\xb3\x01\n\x14strict_check_headers\x18\x05 \x03(\tB\x94\x01\xfa\x42\x90\x01\x92\x01\x8c\x01\"\x89\x01r\x86\x01R\x1ex-envoy-upstream-rq-timeout-msR&x-envoy-upstream-rq-per-try-timeout-msR\x13x-envoy-max-retriesR\x15x-envoy-retry-grpc-onR\x10x-envoy-retry-on\x12#\n\x1brespect_expected_rq_timeout\x18\x06 \x01(\x08\x42\xcc\x01\n0io.envoyproxy.envoy.config.filter.http.router.v2B\x0bRouterProtoP\x01ZRgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/router/v2;routerv2\xf2\x98\xfe\x8f\x05)\x12\'envoy.extensions.filters.http.router.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.router.v2.router_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n0io.envoyproxy.envoy.config.filter.http.router.v2B\013RouterProtoP\001ZRgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/router/v2;routerv2\362\230\376\217\005)\022\'envoy.extensions.filters.http.router.v3\272\200\310\321\006\002\020\001'
  _ROUTER.fields_by_name['strict_check_headers']._options = None
  _ROUTER.fields_by_name['strict_check_headers']._serialized_options = b'\372B\220\001\222\001\214\001\"\211\001r\206\001R\036x-envoy-upstream-rq-timeout-msR&x-envoy-upstream-rq-per-try-timeout-msR\023x-envoy-max-retriesR\025x-envoy-retry-grpc-onR\020x-envoy-retry-on'
  _globals['_ROUTER']._serialized_start=258
  _globals['_ROUTER']._serialized_end=661
# @@protoc_insertion_point(module_scope)
