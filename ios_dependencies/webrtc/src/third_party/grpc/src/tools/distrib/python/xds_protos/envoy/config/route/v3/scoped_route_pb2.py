# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/route/v3/scoped_route.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.route.v3 import route_pb2 as envoy_dot_config_dot_route_dot_v3_dot_route__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(envoy/config/route/v3/scoped_route.proto\x12\x15\x65nvoy.config.route.v3\x1a!envoy/config/route/v3/route.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\xd3\x04\n\x18ScopedRouteConfiguration\x12\x11\n\ton_demand\x18\x04 \x01(\x08\x12\x15\n\x04name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x36\n\x18route_configuration_name\x18\x02 \x01(\tB\x14\xf2\x98\xfe\x8f\x05\x0e\x12\x0croute_config\x12\\\n\x13route_configuration\x18\x05 \x01(\x0b\x32).envoy.config.route.v3.RouteConfigurationB\x14\xf2\x98\xfe\x8f\x05\x0e\x12\x0croute_config\x12J\n\x03key\x18\x03 \x01(\x0b\x32\x33.envoy.config.route.v3.ScopedRouteConfiguration.KeyB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x1a\xfc\x01\n\x03Key\x12Y\n\tfragments\x18\x01 \x03(\x0b\x32<.envoy.config.route.v3.ScopedRouteConfiguration.Key.FragmentB\x08\xfa\x42\x05\x92\x01\x02\x08\x01\x1ah\n\x08\x46ragment\x12\x14\n\nstring_key\x18\x01 \x01(\tH\x00:9\x9a\xc5\x88\x1e\x34\n2envoy.api.v2.ScopedRouteConfiguration.Key.FragmentB\x0b\n\x04type\x12\x03\xf8\x42\x01:0\x9a\xc5\x88\x1e+\n)envoy.api.v2.ScopedRouteConfiguration.Key:,\x9a\xc5\x88\x1e\'\n%envoy.api.v2.ScopedRouteConfigurationB\x87\x01\n#io.envoyproxy.envoy.config.route.v3B\x10ScopedRouteProtoP\x01ZDgithub.com/envoyproxy/go-control-plane/envoy/config/route/v3;routev3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.route.v3.scoped_route_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n#io.envoyproxy.envoy.config.route.v3B\020ScopedRouteProtoP\001ZDgithub.com/envoyproxy/go-control-plane/envoy/config/route/v3;routev3\272\200\310\321\006\002\020\002'
  _SCOPEDROUTECONFIGURATION_KEY_FRAGMENT.oneofs_by_name['type']._options = None
  _SCOPEDROUTECONFIGURATION_KEY_FRAGMENT.oneofs_by_name['type']._serialized_options = b'\370B\001'
  _SCOPEDROUTECONFIGURATION_KEY_FRAGMENT._options = None
  _SCOPEDROUTECONFIGURATION_KEY_FRAGMENT._serialized_options = b'\232\305\210\0364\n2envoy.api.v2.ScopedRouteConfiguration.Key.Fragment'
  _SCOPEDROUTECONFIGURATION_KEY.fields_by_name['fragments']._options = None
  _SCOPEDROUTECONFIGURATION_KEY.fields_by_name['fragments']._serialized_options = b'\372B\005\222\001\002\010\001'
  _SCOPEDROUTECONFIGURATION_KEY._options = None
  _SCOPEDROUTECONFIGURATION_KEY._serialized_options = b'\232\305\210\036+\n)envoy.api.v2.ScopedRouteConfiguration.Key'
  _SCOPEDROUTECONFIGURATION.fields_by_name['name']._options = None
  _SCOPEDROUTECONFIGURATION.fields_by_name['name']._serialized_options = b'\372B\004r\002\020\001'
  _SCOPEDROUTECONFIGURATION.fields_by_name['route_configuration_name']._options = None
  _SCOPEDROUTECONFIGURATION.fields_by_name['route_configuration_name']._serialized_options = b'\362\230\376\217\005\016\022\014route_config'
  _SCOPEDROUTECONFIGURATION.fields_by_name['route_configuration']._options = None
  _SCOPEDROUTECONFIGURATION.fields_by_name['route_configuration']._serialized_options = b'\362\230\376\217\005\016\022\014route_config'
  _SCOPEDROUTECONFIGURATION.fields_by_name['key']._options = None
  _SCOPEDROUTECONFIGURATION.fields_by_name['key']._serialized_options = b'\372B\005\212\001\002\020\001'
  _SCOPEDROUTECONFIGURATION._options = None
  _SCOPEDROUTECONFIGURATION._serialized_options = b'\232\305\210\036\'\n%envoy.api.v2.ScopedRouteConfiguration'
  _globals['_SCOPEDROUTECONFIGURATION']._serialized_start=226
  _globals['_SCOPEDROUTECONFIGURATION']._serialized_end=821
  _globals['_SCOPEDROUTECONFIGURATION_KEY']._serialized_start=523
  _globals['_SCOPEDROUTECONFIGURATION_KEY']._serialized_end=775
  _globals['_SCOPEDROUTECONFIGURATION_KEY_FRAGMENT']._serialized_start=621
  _globals['_SCOPEDROUTECONFIGURATION_KEY_FRAGMENT']._serialized_end=725
# @@protoc_insertion_point(module_scope)
