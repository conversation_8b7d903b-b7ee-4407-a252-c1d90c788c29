# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/dubbo_proxy/v2alpha1/route.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.route import route_components_pb2 as envoy_dot_api_dot_v2_dot_route_dot_route__components__pb2
from envoy.type.matcher import string_pb2 as envoy_dot_type_dot_matcher_dot_string__pb2
from envoy.type import range_pb2 as envoy_dot_type_dot_range__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n<envoy/config/filter/network/dubbo_proxy/v2alpha1/route.proto\x12\x30\x65nvoy.config.filter.network.dubbo_proxy.v2alpha1\x1a)envoy/api/v2/route/route_components.proto\x1a\x1f\x65nvoy/type/matcher/string.proto\x1a\x16\x65nvoy/type/range.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\x9e\x01\n\x12RouteConfiguration\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tinterface\x18\x02 \x01(\t\x12\r\n\x05group\x18\x03 \x01(\t\x12\x0f\n\x07version\x18\x04 \x01(\t\x12G\n\x06routes\x18\x05 \x03(\x0b\x32\x37.envoy.config.filter.network.dubbo_proxy.v2alpha1.Route\"\xb6\x01\n\x05Route\x12U\n\x05match\x18\x01 \x01(\x0b\x32<.envoy.config.filter.network.dubbo_proxy.v2alpha1.RouteMatchB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12V\n\x05route\x18\x02 \x01(\x0b\x32=.envoy.config.filter.network.dubbo_proxy.v2alpha1.RouteActionB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\"\x8f\x01\n\nRouteMatch\x12M\n\x06method\x18\x01 \x01(\x0b\x32=.envoy.config.filter.network.dubbo_proxy.v2alpha1.MethodMatch\x12\x32\n\x07headers\x18\x02 \x03(\x0b\x32!.envoy.api.v2.route.HeaderMatcher\"|\n\x0bRouteAction\x12\x11\n\x07\x63luster\x18\x01 \x01(\tH\x00\x12@\n\x11weighted_clusters\x18\x02 \x01(\x0b\x32#.envoy.api.v2.route.WeightedClusterH\x00\x42\x18\n\x11\x63luster_specifier\x12\x03\xf8\x42\x01\"\xae\x03\n\x0bMethodMatch\x12/\n\x04name\x18\x01 \x01(\x0b\x32!.envoy.type.matcher.StringMatcher\x12\x64\n\x0cparams_match\x18\x02 \x03(\x0b\x32N.envoy.config.filter.network.dubbo_proxy.v2alpha1.MethodMatch.ParamsMatchEntry\x1a|\n\x17ParameterMatchSpecifier\x12\x15\n\x0b\x65xact_match\x18\x03 \x01(\tH\x00\x12-\n\x0brange_match\x18\x04 \x01(\x0b\x32\x16.envoy.type.Int64RangeH\x00\x42\x1b\n\x19parameter_match_specifier\x1a\x89\x01\n\x10ParamsMatchEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x64\n\x05value\x18\x02 \x01(\x0b\x32U.envoy.config.filter.network.dubbo_proxy.v2alpha1.MethodMatch.ParameterMatchSpecifier:\x02\x38\x01\x42\xe6\x01\n>io.envoyproxy.envoy.config.filter.network.dubbo_proxy.v2alpha1B\nRouteProtoP\x01ZWgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/dubbo_proxy/v2alpha1\xf2\x98\xfe\x8f\x05\x31\x12/envoy.extensions.filters.network.dubbo_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.dubbo_proxy.v2alpha1.route_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n>io.envoyproxy.envoy.config.filter.network.dubbo_proxy.v2alpha1B\nRouteProtoP\001ZWgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/dubbo_proxy/v2alpha1\362\230\376\217\0051\022/envoy.extensions.filters.network.dubbo_proxy.v3\272\200\310\321\006\002\020\001'
  _ROUTE.fields_by_name['match']._options = None
  _ROUTE.fields_by_name['match']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTE.fields_by_name['route']._options = None
  _ROUTE.fields_by_name['route']._serialized_options = b'\372B\005\212\001\002\020\001'
  _ROUTEACTION.oneofs_by_name['cluster_specifier']._options = None
  _ROUTEACTION.oneofs_by_name['cluster_specifier']._serialized_options = b'\370B\001'
  _METHODMATCH_PARAMSMATCHENTRY._options = None
  _METHODMATCH_PARAMSMATCHENTRY._serialized_options = b'8\001'
  _globals['_ROUTECONFIGURATION']._serialized_start=303
  _globals['_ROUTECONFIGURATION']._serialized_end=461
  _globals['_ROUTE']._serialized_start=464
  _globals['_ROUTE']._serialized_end=646
  _globals['_ROUTEMATCH']._serialized_start=649
  _globals['_ROUTEMATCH']._serialized_end=792
  _globals['_ROUTEACTION']._serialized_start=794
  _globals['_ROUTEACTION']._serialized_end=918
  _globals['_METHODMATCH']._serialized_start=921
  _globals['_METHODMATCH']._serialized_end=1351
  _globals['_METHODMATCH_PARAMETERMATCHSPECIFIER']._serialized_start=1087
  _globals['_METHODMATCH_PARAMETERMATCHSPECIFIER']._serialized_end=1211
  _globals['_METHODMATCH_PARAMSMATCHENTRY']._serialized_start=1214
  _globals['_METHODMATCH_PARAMSMATCHENTRY']._serialized_end=1351
# @@protoc_insertion_point(module_scope)
