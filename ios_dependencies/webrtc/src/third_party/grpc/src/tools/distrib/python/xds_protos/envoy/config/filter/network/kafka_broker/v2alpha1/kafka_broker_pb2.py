# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/kafka_broker/v2alpha1/kafka_broker.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nDenvoy/config/filter/network/kafka_broker/v2alpha1/kafka_broker.proto\x12\x31\x65nvoy.config.filter.network.kafka_broker.v2alpha1\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xbe\x02\n\x0bKafkaBroker\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x1e\n\x16\x66orce_response_rewrite\x18\x02 \x01(\x08\x12{\n$id_based_broker_address_rewrite_spec\x18\x03 \x01(\x0b\x32K.envoy.config.filter.network.kafka_broker.v2alpha1.IdBasedBrokerRewriteSpecH\x00\x12*\n\x10\x61pi_keys_allowed\x18\x04 \x03(\rB\x10\xfa\x42\r\x92\x01\n\"\x08*\x06\x18\xff\xff\x01(\x00\x12)\n\x0f\x61pi_keys_denied\x18\x05 \x03(\rB\x10\xfa\x42\r\x92\x01\n\"\x08*\x06\x18\xff\xff\x01(\x00\x42\x1d\n\x1b\x62roker_address_rewrite_spec\"v\n\x18IdBasedBrokerRewriteSpec\x12Z\n\x05rules\x18\x01 \x03(\x0b\x32K.envoy.config.filter.network.kafka_broker.v2alpha1.IdBasedBrokerRewriteRule\"_\n\x18IdBasedBrokerRewriteRule\x12\x13\n\x02id\x18\x01 \x01(\rB\x07\xfa\x42\x04*\x02(\x00\x12\x15\n\x04host\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x17\n\x04port\x18\x03 \x01(\rB\t\xfa\x42\x06*\x04\x18\xff\xff\x03\x42\xef\x01\n?io.envoyproxy.envoy.config.filter.network.kafka_broker.v2alpha1B\x10KafkaBrokerProtoP\x01ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/kafka_broker/v2alpha1\xf2\x98\xfe\x8f\x05\x32\x12\x30\x65nvoy.extensions.filters.network.kafka_broker.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.kafka_broker.v2alpha1.kafka_broker_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n?io.envoyproxy.envoy.config.filter.network.kafka_broker.v2alpha1B\020KafkaBrokerProtoP\001ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/network/kafka_broker/v2alpha1\362\230\376\217\0052\0220envoy.extensions.filters.network.kafka_broker.v3\272\200\310\321\006\002\020\001'
  _KAFKABROKER.fields_by_name['stat_prefix']._options = None
  _KAFKABROKER.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _KAFKABROKER.fields_by_name['api_keys_allowed']._options = None
  _KAFKABROKER.fields_by_name['api_keys_allowed']._serialized_options = b'\372B\r\222\001\n\"\010*\006\030\377\377\001(\000'
  _KAFKABROKER.fields_by_name['api_keys_denied']._options = None
  _KAFKABROKER.fields_by_name['api_keys_denied']._serialized_options = b'\372B\r\222\001\n\"\010*\006\030\377\377\001(\000'
  _IDBASEDBROKERREWRITERULE.fields_by_name['id']._options = None
  _IDBASEDBROKERREWRITERULE.fields_by_name['id']._serialized_options = b'\372B\004*\002(\000'
  _IDBASEDBROKERREWRITERULE.fields_by_name['host']._options = None
  _IDBASEDBROKERREWRITERULE.fields_by_name['host']._serialized_options = b'\372B\004r\002\020\001'
  _IDBASEDBROKERREWRITERULE.fields_by_name['port']._options = None
  _IDBASEDBROKERREWRITERULE.fields_by_name['port']._serialized_options = b'\372B\006*\004\030\377\377\003'
  _globals['_KAFKABROKER']._serialized_start=212
  _globals['_KAFKABROKER']._serialized_end=530
  _globals['_IDBASEDBROKERREWRITESPEC']._serialized_start=532
  _globals['_IDBASEDBROKERREWRITESPEC']._serialized_end=650
  _globals['_IDBASEDBROKERREWRITERULE']._serialized_start=652
  _globals['_IDBASEDBROKERREWRITERULE']._serialized_end=747
# @@protoc_insertion_point(module_scope)
