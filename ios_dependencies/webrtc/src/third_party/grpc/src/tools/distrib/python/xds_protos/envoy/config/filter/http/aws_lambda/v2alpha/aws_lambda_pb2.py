# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/aws_lambda/v2alpha/aws_lambda.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n<envoy/config/filter/http/aws_lambda/v2alpha/aws_lambda.proto\x12+envoy.config.filter.http.aws_lambda.v2alpha\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xd7\x01\n\x06\x43onfig\x12\x14\n\x03\x61rn\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01\x12\x1b\n\x13payload_passthrough\x18\x02 \x01(\x08\x12\x65\n\x0finvocation_mode\x18\x03 \x01(\x0e\x32\x42.envoy.config.filter.http.aws_lambda.v2alpha.Config.InvocationModeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\"3\n\x0eInvocationMode\x12\x0f\n\x0bSYNCHRONOUS\x10\x00\x12\x10\n\x0c\x41SYNCHRONOUS\x10\x01\"\\\n\x0ePerRouteConfig\x12J\n\rinvoke_config\x18\x01 \x01(\x0b\x32\x33.envoy.config.filter.http.aws_lambda.v2alpha.ConfigB\xde\x01\n9io.envoyproxy.envoy.config.filter.http.aws_lambda.v2alphaB\x0e\x41wsLambdaProtoP\x01ZRgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/aws_lambda/v2alpha\xf2\x98\xfe\x8f\x05-\x12+envoy.extensions.filters.http.aws_lambda.v3\xba\x80\xc8\xd1\x06\x04\x08\x01\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.aws_lambda.v2alpha.aws_lambda_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n9io.envoyproxy.envoy.config.filter.http.aws_lambda.v2alphaB\016AwsLambdaProtoP\001ZRgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/aws_lambda/v2alpha\362\230\376\217\005-\022+envoy.extensions.filters.http.aws_lambda.v3\272\200\310\321\006\004\010\001\020\001'
  _CONFIG.fields_by_name['arn']._options = None
  _CONFIG.fields_by_name['arn']._serialized_options = b'\372B\004r\002\020\001'
  _CONFIG.fields_by_name['invocation_mode']._options = None
  _CONFIG.fields_by_name['invocation_mode']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_CONFIG']._serialized_start=198
  _globals['_CONFIG']._serialized_end=413
  _globals['_CONFIG_INVOCATIONMODE']._serialized_start=362
  _globals['_CONFIG_INVOCATIONMODE']._serialized_end=413
  _globals['_PERROUTECONFIG']._serialized_start=415
  _globals['_PERROUTECONFIG']._serialized_end=507
# @@protoc_insertion_point(module_scope)
