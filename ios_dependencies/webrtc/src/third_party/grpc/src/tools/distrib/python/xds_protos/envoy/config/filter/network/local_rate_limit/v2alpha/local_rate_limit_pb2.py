# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/local_rate_limit/v2alpha/local_rate_limit.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.type import token_bucket_pb2 as envoy_dot_type_dot_token__bucket__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nKenvoy/config/filter/network/local_rate_limit/v2alpha/local_rate_limit.proto\x12\x34\x65nvoy.config.filter.network.local_rate_limit.v2alpha\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a\x1d\x65nvoy/type/token_bucket.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xa7\x01\n\x0eLocalRateLimit\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x37\n\x0ctoken_bucket\x18\x02 \x01(\x0b\x32\x17.envoy.type.TokenBucketB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12>\n\x0fruntime_enabled\x18\x03 \x01(\x0b\x32%.envoy.api.v2.core.RuntimeFeatureFlagB\xfb\x01\nBio.envoyproxy.envoy.config.filter.network.local_rate_limit.v2alphaB\x13LocalRateLimitProtoP\x01Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/local_rate_limit/v2alpha\xf2\x98\xfe\x8f\x05\x35\x12\x33\x65nvoy.extensions.filters.network.local_ratelimit.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.local_rate_limit.v2alpha.local_rate_limit_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\nBio.envoyproxy.envoy.config.filter.network.local_rate_limit.v2alphaB\023LocalRateLimitProtoP\001Z[github.com/envoyproxy/go-control-plane/envoy/config/filter/network/local_rate_limit/v2alpha\362\230\376\217\0055\0223envoy.extensions.filters.network.local_ratelimit.v3\272\200\310\321\006\002\020\001'
  _LOCALRATELIMIT.fields_by_name['stat_prefix']._options = None
  _LOCALRATELIMIT.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _LOCALRATELIMIT.fields_by_name['token_bucket']._options = None
  _LOCALRATELIMIT.fields_by_name['token_bucket']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LOCALRATELIMIT']._serialized_start=283
  _globals['_LOCALRATELIMIT']._serialized_end=450
# @@protoc_insertion_point(module_scope)
