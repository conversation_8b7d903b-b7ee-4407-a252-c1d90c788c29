# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/network/redis_proxy/v2/redis_proxy.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from envoy.annotations import deprecation_pb2 as envoy_dot_annotations_dot_deprecation__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import sensitive_pb2 as udpa_dot_annotations_dot_sensitive__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n<envoy/config/filter/network/redis_proxy/v2/redis_proxy.proto\x12*envoy.config.filter.network.redis_proxy.v2\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a#envoy/annotations/deprecation.proto\x1a\x1eudpa/annotations/migrate.proto\x1a udpa/annotations/sensitive.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xef\x0b\n\nRedisProxy\x12\x1c\n\x0bstat_prefix\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x19\n\x07\x63luster\x18\x02 \x01(\tB\x08\x18\x01\xb8\xee\xf2\xd2\x05\x01\x12\x63\n\x08settings\x18\x03 \x01(\x0b\x32G.envoy.config.filter.network.redis_proxy.v2.RedisProxy.ConnPoolSettingsB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01\x12\x19\n\x11latency_in_micros\x18\x04 \x01(\x08\x12Z\n\rprefix_routes\x18\x05 \x01(\x0b\x32\x43.envoy.config.filter.network.redis_proxy.v2.RedisProxy.PrefixRoutes\x12G\n\x18\x64ownstream_auth_password\x18\x06 \x01(\x0b\x32\x1d.envoy.api.v2.core.DataSourceB\x06\xb8\xb7\x8b\xa4\x02\x01\x1a\x92\x04\n\x10\x43onnPoolSettings\x12\x37\n\nop_timeout\x18\x01 \x01(\x0b\x32\x19.google.protobuf.DurationB\x08\xfa\x42\x05\xaa\x01\x02\x08\x01\x12\x1a\n\x12\x65nable_hashtagging\x18\x02 \x01(\x08\x12\x1a\n\x12\x65nable_redirection\x18\x03 \x01(\x08\x12$\n\x1cmax_buffer_size_before_flush\x18\x04 \x01(\r\x12\x37\n\x14\x62uffer_flush_timeout\x18\x05 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x46\n max_upstream_unknown_connections\x18\x06 \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12\x1c\n\x14\x65nable_command_stats\x18\x08 \x01(\x08\x12q\n\x0bread_policy\x18\x07 \x01(\x0e\x32R.envoy.config.filter.network.redis_proxy.v2.RedisProxy.ConnPoolSettings.ReadPolicyB\x08\xfa\x42\x05\x82\x01\x02\x10\x01\"U\n\nReadPolicy\x12\n\n\x06MASTER\x10\x00\x12\x11\n\rPREFER_MASTER\x10\x01\x12\x0b\n\x07REPLICA\x10\x02\x12\x12\n\x0ePREFER_REPLICA\x10\x03\x12\x07\n\x03\x41NY\x10\x04\x1a\xed\x04\n\x0cPrefixRoutes\x12Y\n\x06routes\x18\x01 \x03(\x0b\x32I.envoy.config.filter.network.redis_proxy.v2.RedisProxy.PrefixRoutes.Route\x12\x18\n\x10\x63\x61se_insensitive\x18\x02 \x01(\x08\x12#\n\x11\x63\x61tch_all_cluster\x18\x03 \x01(\tB\x08\x18\x01\xb8\xee\xf2\xd2\x05\x01\x12\x62\n\x0f\x63\x61tch_all_route\x18\x04 \x01(\x0b\x32I.envoy.config.filter.network.redis_proxy.v2.RedisProxy.PrefixRoutes.Route\x1a\xde\x02\n\x05Route\x12\x0e\n\x06prefix\x18\x01 \x01(\t\x12\x15\n\rremove_prefix\x18\x02 \x01(\x08\x12\x18\n\x07\x63luster\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12|\n\x15request_mirror_policy\x18\x04 \x03(\x0b\x32].envoy.config.filter.network.redis_proxy.v2.RedisProxy.PrefixRoutes.Route.RequestMirrorPolicy\x1a\x95\x01\n\x13RequestMirrorPolicy\x12\x18\n\x07\x63luster\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02 \x01\x12\x45\n\x10runtime_fraction\x18\x02 \x01(\x0b\x32+.envoy.api.v2.core.RuntimeFractionalPercent\x12\x1d\n\x15\x65xclude_read_commands\x18\x03 \x01(\x08\"T\n\x14RedisProtocolOptions\x12<\n\rauth_password\x18\x01 \x01(\x0b\x32\x1d.envoy.api.v2.core.DataSourceB\x06\xb8\xb7\x8b\xa4\x02\x01\x42\xed\x01\n8io.envoyproxy.envoy.config.filter.network.redis_proxy.v2B\x0fRedisProxyProtoP\x01Z_github.com/envoyproxy/go-control-plane/envoy/config/filter/network/redis_proxy/v2;redis_proxyv2\xf2\x98\xfe\x8f\x05\x31\x12/envoy.extensions.filters.network.redis_proxy.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.network.redis_proxy.v2.redis_proxy_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n8io.envoyproxy.envoy.config.filter.network.redis_proxy.v2B\017RedisProxyProtoP\001Z_github.com/envoyproxy/go-control-plane/envoy/config/filter/network/redis_proxy/v2;redis_proxyv2\362\230\376\217\0051\022/envoy.extensions.filters.network.redis_proxy.v3\272\200\310\321\006\002\020\001'
  _REDISPROXY_CONNPOOLSETTINGS.fields_by_name['op_timeout']._options = None
  _REDISPROXY_CONNPOOLSETTINGS.fields_by_name['op_timeout']._serialized_options = b'\372B\005\252\001\002\010\001'
  _REDISPROXY_CONNPOOLSETTINGS.fields_by_name['read_policy']._options = None
  _REDISPROXY_CONNPOOLSETTINGS.fields_by_name['read_policy']._serialized_options = b'\372B\005\202\001\002\020\001'
  _REDISPROXY_PREFIXROUTES_ROUTE_REQUESTMIRRORPOLICY.fields_by_name['cluster']._options = None
  _REDISPROXY_PREFIXROUTES_ROUTE_REQUESTMIRRORPOLICY.fields_by_name['cluster']._serialized_options = b'\372B\004r\002 \001'
  _REDISPROXY_PREFIXROUTES_ROUTE.fields_by_name['cluster']._options = None
  _REDISPROXY_PREFIXROUTES_ROUTE.fields_by_name['cluster']._serialized_options = b'\372B\004r\002 \001'
  _REDISPROXY_PREFIXROUTES.fields_by_name['catch_all_cluster']._options = None
  _REDISPROXY_PREFIXROUTES.fields_by_name['catch_all_cluster']._serialized_options = b'\030\001\270\356\362\322\005\001'
  _REDISPROXY.fields_by_name['stat_prefix']._options = None
  _REDISPROXY.fields_by_name['stat_prefix']._serialized_options = b'\372B\004r\002 \001'
  _REDISPROXY.fields_by_name['cluster']._options = None
  _REDISPROXY.fields_by_name['cluster']._serialized_options = b'\030\001\270\356\362\322\005\001'
  _REDISPROXY.fields_by_name['settings']._options = None
  _REDISPROXY.fields_by_name['settings']._serialized_options = b'\372B\005\212\001\002\020\001'
  _REDISPROXY.fields_by_name['downstream_auth_password']._options = None
  _REDISPROXY.fields_by_name['downstream_auth_password']._serialized_options = b'\270\267\213\244\002\001'
  _REDISPROTOCOLOPTIONS.fields_by_name['auth_password']._options = None
  _REDISPROTOCOLOPTIONS.fields_by_name['auth_password']._serialized_options = b'\270\267\213\244\002\001'
  _globals['_REDISPROXY']._serialized_start=362
  _globals['_REDISPROXY']._serialized_end=1881
  _globals['_REDISPROXY_CONNPOOLSETTINGS']._serialized_start=727
  _globals['_REDISPROXY_CONNPOOLSETTINGS']._serialized_end=1257
  _globals['_REDISPROXY_CONNPOOLSETTINGS_READPOLICY']._serialized_start=1172
  _globals['_REDISPROXY_CONNPOOLSETTINGS_READPOLICY']._serialized_end=1257
  _globals['_REDISPROXY_PREFIXROUTES']._serialized_start=1260
  _globals['_REDISPROXY_PREFIXROUTES']._serialized_end=1881
  _globals['_REDISPROXY_PREFIXROUTES_ROUTE']._serialized_start=1531
  _globals['_REDISPROXY_PREFIXROUTES_ROUTE']._serialized_end=1881
  _globals['_REDISPROXY_PREFIXROUTES_ROUTE_REQUESTMIRRORPOLICY']._serialized_start=1732
  _globals['_REDISPROXY_PREFIXROUTES_ROUTE_REQUESTMIRRORPOLICY']._serialized_end=1881
  _globals['_REDISPROTOCOLOPTIONS']._serialized_start=1883
  _globals['_REDISPROTOCOLOPTIONS']._serialized_end=1967
# @@protoc_insertion_point(module_scope)
