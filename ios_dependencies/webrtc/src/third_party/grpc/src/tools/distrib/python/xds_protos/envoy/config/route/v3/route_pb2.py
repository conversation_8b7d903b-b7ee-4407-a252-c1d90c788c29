# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/route/v3/route.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.config.core.v3 import base_pb2 as envoy_dot_config_dot_core_dot_v3_dot_base__pb2
from envoy.config.core.v3 import config_source_pb2 as envoy_dot_config_dot_core_dot_v3_dot_config__source__pb2
from envoy.config.route.v3 import route_components_pb2 as envoy_dot_config_dot_route_dot_v3_dot_route__components__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from udpa.annotations import versioning_pb2 as udpa_dot_annotations_dot_versioning__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!envoy/config/route/v3/route.proto\x12\x15\x65nvoy.config.route.v3\x1a\x1f\x65nvoy/config/core/v3/base.proto\x1a(envoy/config/core/v3/config_source.proto\x1a,envoy/config/route/v3/route_components.proto\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x1dudpa/annotations/status.proto\x1a!udpa/annotations/versioning.proto\x1a\x17validate/validate.proto\"\x91\t\n\x12RouteConfiguration\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x39\n\rvirtual_hosts\x18\x02 \x03(\x0b\x32\".envoy.config.route.v3.VirtualHost\x12)\n\x04vhds\x18\t \x01(\x0b\x32\x1b.envoy.config.route.v3.Vhds\x12/\n\x15internal_only_headers\x18\x03 \x03(\tB\x10\xfa\x42\r\x92\x01\n\"\x08r\x06\xc0\x01\x01\xc8\x01\x00\x12S\n\x17response_headers_to_add\x18\x04 \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x34\n\x1aresponse_headers_to_remove\x18\x05 \x03(\tB\x10\xfa\x42\r\x92\x01\n\"\x08r\x06\xc0\x01\x01\xc8\x01\x00\x12R\n\x16request_headers_to_add\x18\x06 \x03(\x0b\x32\'.envoy.config.core.v3.HeaderValueOptionB\t\xfa\x42\x06\x92\x01\x03\x10\xe8\x07\x12\x33\n\x19request_headers_to_remove\x18\x08 \x03(\tB\x10\xfa\x42\r\x92\x01\n\"\x08r\x06\xc0\x01\x01\xc8\x01\x00\x12+\n#most_specific_header_mutations_wins\x18\n \x01(\x08\x12\x35\n\x11validate_clusters\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12I\n#max_direct_response_body_size_bytes\x18\x0b \x01(\x0b\x32\x1c.google.protobuf.UInt32Value\x12P\n\x19\x63luster_specifier_plugins\x18\x0c \x03(\x0b\x32-.envoy.config.route.v3.ClusterSpecifierPlugin\x12W\n\x17request_mirror_policies\x18\r \x03(\x0b\x32\x36.envoy.config.route.v3.RouteAction.RequestMirrorPolicy\x12$\n\x1cignore_port_in_host_matching\x18\x0e \x01(\x08\x12/\n\'ignore_path_parameters_in_path_matching\x18\x0f \x01(\x08\x12\x64\n\x17typed_per_filter_config\x18\x10 \x03(\x0b\x32\x43.envoy.config.route.v3.RouteConfiguration.TypedPerFilterConfigEntry\x12\x30\n\x08metadata\x18\x11 \x01(\x0b\x32\x1e.envoy.config.core.v3.Metadata\x1aQ\n\x19TypedPerFilterConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any:\x02\x38\x01:&\x9a\xc5\x88\x1e!\n\x1f\x65nvoy.api.v2.RouteConfiguration\"e\n\x04Vhds\x12\x43\n\rconfig_source\x18\x01 \x01(\x0b\x32\".envoy.config.core.v3.ConfigSourceB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01:\x18\x9a\xc5\x88\x1e\x13\n\x11\x65nvoy.api.v2.VhdsB\x81\x01\n#io.envoyproxy.envoy.config.route.v3B\nRouteProtoP\x01ZDgithub.com/envoyproxy/go-control-plane/envoy/config/route/v3;routev3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.route.v3.route_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n#io.envoyproxy.envoy.config.route.v3B\nRouteProtoP\001ZDgithub.com/envoyproxy/go-control-plane/envoy/config/route/v3;routev3\272\200\310\321\006\002\020\002'
  _ROUTECONFIGURATION_TYPEDPERFILTERCONFIGENTRY._options = None
  _ROUTECONFIGURATION_TYPEDPERFILTERCONFIGENTRY._serialized_options = b'8\001'
  _ROUTECONFIGURATION.fields_by_name['internal_only_headers']._options = None
  _ROUTECONFIGURATION.fields_by_name['internal_only_headers']._serialized_options = b'\372B\r\222\001\n\"\010r\006\300\001\001\310\001\000'
  _ROUTECONFIGURATION.fields_by_name['response_headers_to_add']._options = None
  _ROUTECONFIGURATION.fields_by_name['response_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _ROUTECONFIGURATION.fields_by_name['response_headers_to_remove']._options = None
  _ROUTECONFIGURATION.fields_by_name['response_headers_to_remove']._serialized_options = b'\372B\r\222\001\n\"\010r\006\300\001\001\310\001\000'
  _ROUTECONFIGURATION.fields_by_name['request_headers_to_add']._options = None
  _ROUTECONFIGURATION.fields_by_name['request_headers_to_add']._serialized_options = b'\372B\006\222\001\003\020\350\007'
  _ROUTECONFIGURATION.fields_by_name['request_headers_to_remove']._options = None
  _ROUTECONFIGURATION.fields_by_name['request_headers_to_remove']._serialized_options = b'\372B\r\222\001\n\"\010r\006\300\001\001\310\001\000'
  _ROUTECONFIGURATION._options = None
  _ROUTECONFIGURATION._serialized_options = b'\232\305\210\036!\n\037envoy.api.v2.RouteConfiguration'
  _VHDS.fields_by_name['config_source']._options = None
  _VHDS.fields_by_name['config_source']._serialized_options = b'\372B\005\212\001\002\020\001'
  _VHDS._options = None
  _VHDS._serialized_options = b'\232\305\210\036\023\n\021envoy.api.v2.Vhds'
  _globals['_ROUTECONFIGURATION']._serialized_start=332
  _globals['_ROUTECONFIGURATION']._serialized_end=1501
  _globals['_ROUTECONFIGURATION_TYPEDPERFILTERCONFIGENTRY']._serialized_start=1380
  _globals['_ROUTECONFIGURATION_TYPEDPERFILTERCONFIGENTRY']._serialized_end=1461
  _globals['_VHDS']._serialized_start=1503
  _globals['_VHDS']._serialized_end=1604
# @@protoc_insertion_point(module_scope)
