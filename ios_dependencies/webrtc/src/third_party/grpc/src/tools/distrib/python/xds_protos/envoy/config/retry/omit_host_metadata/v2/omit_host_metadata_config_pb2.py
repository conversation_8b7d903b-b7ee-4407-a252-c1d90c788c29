# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/retry/omit_host_metadata/v2/omit_host_metadata_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nHenvoy/config/retry/omit_host_metadata/v2/omit_host_metadata_config.proto\x12(envoy.config.retry.omit_host_metadata.v2\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"M\n\x16OmitHostMetadataConfig\x12\x33\n\x0emetadata_match\x18\x01 \x01(\x0b\x32\x1b.envoy.api.v2.core.MetadataB\xfe\x01\n6io.envoyproxy.envoy.config.retry.omit_host_metadata.v2B\x1bOmitHostMetadataConfigProtoP\x01Zdgithub.com/envoyproxy/go-control-plane/envoy/config/retry/omit_host_metadata/v2;omit_host_metadatav2\xf2\x98\xfe\x8f\x05\x33\x12\x31\x65nvoy.extensions.retry.host.omit_host_metadata.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.retry.omit_host_metadata.v2.omit_host_metadata_config_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n6io.envoyproxy.envoy.config.retry.omit_host_metadata.v2B\033OmitHostMetadataConfigProtoP\001Zdgithub.com/envoyproxy/go-control-plane/envoy/config/retry/omit_host_metadata/v2;omit_host_metadatav2\362\230\376\217\0053\0221envoy.extensions.retry.host.omit_host_metadata.v3\272\200\310\321\006\002\020\001'
  _globals['_OMITHOSTMETADATACONFIG']._serialized_start=211
  _globals['_OMITHOSTMETADATACONFIG']._serialized_end=288
# @@protoc_insertion_point(module_scope)
