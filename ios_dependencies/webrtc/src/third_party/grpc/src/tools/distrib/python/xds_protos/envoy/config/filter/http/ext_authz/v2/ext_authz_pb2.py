# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/filter/http/ext_authz/v2/ext_authz.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from envoy.api.v2.core import base_pb2 as envoy_dot_api_dot_v2_dot_core_dot_base__pb2
try:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2
except AttributeError:
  envoy_dot_api_dot_v2_dot_core_dot_socket__option__pb2 = envoy_dot_api_dot_v2_dot_core_dot_base__pb2.envoy.api.v2.core.socket_option_pb2
from envoy.api.v2.core import grpc_service_pb2 as envoy_dot_api_dot_v2_dot_core_dot_grpc__service__pb2
from envoy.api.v2.core import http_uri_pb2 as envoy_dot_api_dot_v2_dot_core_dot_http__uri__pb2
from envoy.type import http_status_pb2 as envoy_dot_type_dot_http__status__pb2
from envoy.type.matcher import string_pb2 as envoy_dot_type_dot_matcher_dot_string__pb2
from envoy.annotations import deprecation_pb2 as envoy_dot_annotations_dot_deprecation__pb2
from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5envoy/config/filter/http/ext_authz/v2/ext_authz.proto\x12%envoy.config.filter.http.ext_authz.v2\x1a\x1c\x65nvoy/api/v2/core/base.proto\x1a$envoy/api/v2/core/grpc_service.proto\x1a envoy/api/v2/core/http_uri.proto\x1a\x1c\x65nvoy/type/http_status.proto\x1a\x1f\x65nvoy/type/matcher/string.proto\x1a#envoy/annotations/deprecation.proto\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\x1a\x17validate/validate.proto\"\xbd\x04\n\x08\x45xtAuthz\x12\x36\n\x0cgrpc_service\x18\x01 \x01(\x0b\x32\x1e.envoy.api.v2.core.GrpcServiceH\x00\x12J\n\x0chttp_service\x18\x03 \x01(\x0b\x32\x32.envoy.config.filter.http.ext_authz.v2.HttpServiceH\x00\x12\x1a\n\x12\x66\x61ilure_mode_allow\x18\x02 \x01(\x08\x12\x1b\n\tuse_alpha\x18\x04 \x01(\x08\x42\x08\x18\x01\xb8\xee\xf2\xd2\x05\x01\x12P\n\x11with_request_body\x18\x05 \x01(\x0b\x32\x35.envoy.config.filter.http.ext_authz.v2.BufferSettings\x12\x19\n\x11\x63lear_route_cache\x18\x06 \x01(\x08\x12/\n\x0fstatus_on_error\x18\x07 \x01(\x0b\x32\x16.envoy.type.HttpStatus\x12#\n\x1bmetadata_context_namespaces\x18\x08 \x03(\t\x12\x43\n\x0e\x66ilter_enabled\x18\t \x01(\x0b\x32+.envoy.api.v2.core.RuntimeFractionalPercent\x12>\n\x0f\x64\x65ny_at_disable\x18\x0b \x01(\x0b\x32%.envoy.api.v2.core.RuntimeFeatureFlag\x12 \n\x18include_peer_certificate\x18\n \x01(\x08\x42\n\n\x08services\"S\n\x0e\x42ufferSettings\x12\"\n\x11max_request_bytes\x18\x01 \x01(\rB\x07\xfa\x42\x04*\x02 \x00\x12\x1d\n\x15\x61llow_partial_message\x18\x02 \x01(\x08\"\xa4\x02\n\x0bHttpService\x12.\n\nserver_uri\x18\x01 \x01(\x0b\x32\x1a.envoy.api.v2.core.HttpUri\x12\x13\n\x0bpath_prefix\x18\x02 \x01(\t\x12Z\n\x15\x61uthorization_request\x18\x07 \x01(\x0b\x32;.envoy.config.filter.http.ext_authz.v2.AuthorizationRequest\x12\\\n\x16\x61uthorization_response\x18\x08 \x01(\x0b\x32<.envoy.config.filter.http.ext_authz.v2.AuthorizationResponseJ\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07\"\x8e\x01\n\x14\x41uthorizationRequest\x12>\n\x0f\x61llowed_headers\x18\x01 \x01(\x0b\x32%.envoy.type.matcher.ListStringMatcher\x12\x36\n\x0eheaders_to_add\x18\x02 \x03(\x0b\x32\x1e.envoy.api.v2.core.HeaderValue\"\xa7\x01\n\x15\x41uthorizationResponse\x12G\n\x18\x61llowed_upstream_headers\x18\x01 \x01(\x0b\x32%.envoy.type.matcher.ListStringMatcher\x12\x45\n\x16\x61llowed_client_headers\x18\x02 \x01(\x0b\x32%.envoy.type.matcher.ListStringMatcher\"\x9a\x01\n\x10\x45xtAuthzPerRoute\x12\x1b\n\x08\x64isabled\x18\x01 \x01(\x08\x42\x07\xfa\x42\x04j\x02\x08\x01H\x00\x12X\n\x0e\x63heck_settings\x18\x02 \x01(\x0b\x32\x34.envoy.config.filter.http.ext_authz.v2.CheckSettingsB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01H\x00\x42\x0f\n\x08override\x12\x03\xf8\x42\x01\"\xb2\x01\n\rCheckSettings\x12g\n\x12\x63ontext_extensions\x18\x01 \x03(\x0b\x32K.envoy.config.filter.http.ext_authz.v2.CheckSettings.ContextExtensionsEntry\x1a\x38\n\x16\x43ontextExtensionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\xda\x01\n3io.envoyproxy.envoy.config.filter.http.ext_authz.v2B\rExtAuthzProtoP\x01ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/ext_authz/v2;ext_authzv2\xf2\x98\xfe\x8f\x05,\x12*envoy.extensions.filters.http.ext_authz.v3\xba\x80\xc8\xd1\x06\x02\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.filter.http.ext_authz.v2.ext_authz_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n3io.envoyproxy.envoy.config.filter.http.ext_authz.v2B\rExtAuthzProtoP\001ZXgithub.com/envoyproxy/go-control-plane/envoy/config/filter/http/ext_authz/v2;ext_authzv2\362\230\376\217\005,\022*envoy.extensions.filters.http.ext_authz.v3\272\200\310\321\006\002\020\001'
  _EXTAUTHZ.fields_by_name['use_alpha']._options = None
  _EXTAUTHZ.fields_by_name['use_alpha']._serialized_options = b'\030\001\270\356\362\322\005\001'
  _BUFFERSETTINGS.fields_by_name['max_request_bytes']._options = None
  _BUFFERSETTINGS.fields_by_name['max_request_bytes']._serialized_options = b'\372B\004*\002 \000'
  _EXTAUTHZPERROUTE.oneofs_by_name['override']._options = None
  _EXTAUTHZPERROUTE.oneofs_by_name['override']._serialized_options = b'\370B\001'
  _EXTAUTHZPERROUTE.fields_by_name['disabled']._options = None
  _EXTAUTHZPERROUTE.fields_by_name['disabled']._serialized_options = b'\372B\004j\002\010\001'
  _EXTAUTHZPERROUTE.fields_by_name['check_settings']._options = None
  _EXTAUTHZPERROUTE.fields_by_name['check_settings']._serialized_options = b'\372B\005\212\001\002\020\001'
  _CHECKSETTINGS_CONTEXTEXTENSIONSENTRY._options = None
  _CHECKSETTINGS_CONTEXTEXTENSIONSENTRY._serialized_options = b'8\001'
  _globals['_EXTAUTHZ']._serialized_start=387
  _globals['_EXTAUTHZ']._serialized_end=960
  _globals['_BUFFERSETTINGS']._serialized_start=962
  _globals['_BUFFERSETTINGS']._serialized_end=1045
  _globals['_HTTPSERVICE']._serialized_start=1048
  _globals['_HTTPSERVICE']._serialized_end=1340
  _globals['_AUTHORIZATIONREQUEST']._serialized_start=1343
  _globals['_AUTHORIZATIONREQUEST']._serialized_end=1485
  _globals['_AUTHORIZATIONRESPONSE']._serialized_start=1488
  _globals['_AUTHORIZATIONRESPONSE']._serialized_end=1655
  _globals['_EXTAUTHZPERROUTE']._serialized_start=1658
  _globals['_EXTAUTHZPERROUTE']._serialized_end=1812
  _globals['_CHECKSETTINGS']._serialized_start=1815
  _globals['_CHECKSETTINGS']._serialized_end=1993
  _globals['_CHECKSETTINGS_CONTEXTEXTENSIONSENTRY']._serialized_start=1937
  _globals['_CHECKSETTINGS_CONTEXTEXTENSIONSENTRY']._serialized_end=1993
# @@protoc_insertion_point(module_scope)
