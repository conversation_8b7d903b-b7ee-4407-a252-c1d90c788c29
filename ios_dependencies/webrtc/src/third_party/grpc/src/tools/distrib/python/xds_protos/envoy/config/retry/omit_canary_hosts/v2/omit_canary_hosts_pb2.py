# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: envoy/config/retry/omit_canary_hosts/v2/omit_canary_hosts.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from udpa.annotations import migrate_pb2 as udpa_dot_annotations_dot_migrate__pb2
from udpa.annotations import status_pb2 as udpa_dot_annotations_dot_status__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?envoy/config/retry/omit_canary_hosts/v2/omit_canary_hosts.proto\x12\'envoy.config.retry.omit_canary_hosts.v2\x1a\x1eudpa/annotations/migrate.proto\x1a\x1dudpa/annotations/status.proto\"\x1a\n\x18OmitCanaryHostsPredicateB\xf3\x01\n5io.envoyproxy.envoy.config.retry.omit_canary_hosts.v2B\x14OmitCanaryHostsProtoP\x01Zbgithub.com/envoyproxy/go-control-plane/envoy/config/retry/omit_canary_hosts/v2;omit_canary_hostsv2\xf2\x98\xfe\x8f\x05\x32\x12\x30\x65nvoy.extensions.retry.host.omit_canary_hosts.v3\xba\x80\xc8\xd1\x06\x02\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'envoy.config.retry.omit_canary_hosts.v2.omit_canary_hosts_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n5io.envoyproxy.envoy.config.retry.omit_canary_hosts.v2B\024OmitCanaryHostsProtoP\001Zbgithub.com/envoyproxy/go-control-plane/envoy/config/retry/omit_canary_hosts/v2;omit_canary_hostsv2\362\230\376\217\0052\0220envoy.extensions.retry.host.omit_canary_hosts.v3\272\200\310\321\006\002\020\002'
  _globals['_OMITCANARYHOSTSPREDICATE']._serialized_start=171
  _globals['_OMITCANARYHOSTSPREDICATE']._serialized_end=197
# @@protoc_insertion_point(module_scope)
