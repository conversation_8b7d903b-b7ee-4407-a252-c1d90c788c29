name: PR Auto Tag
on:
  pull_request_target:
    types: [opened, reopened, synchronize, edited]
permissions:
  contents: read # to determine modified files (actions/labeler)

jobs:
  triage:
    permissions:
      contents: read # to determine modified files (actions/labeler)
      pull-requests: write # to add labels to PRs (actions/labeler)

    runs-on: ubuntu-latest
    steps:
    - uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9 # v5.0.0
      with:
        repo-token: "${{ secrets.GITHUB_TOKEN }}"
        sync-labels: false
