name: PR AutoFix
on: [push]
permissions: {}
jobs:
  PRAutoFix:
    permissions:
      actions: write # to cancel/stop running workflows (styfle/cancel-workflow-action)
      contents: write # to create branch (peter-evans/create-pull-request)
      pull-requests: write # to create a PR (peter-evans/create-pull-request)

    runs-on: ubuntu-latest
    steps:
      # Cache bazel build
      - name: Get current time
        uses: srfrnk/current-time@5a4163ad035ccd9a407ec9e519c3b6ba1b633d1e # v1.1.0
        id: current-time
        with:
          format: YYYYWW
      - name: Get current time
        uses: srfrnk/current-time@5a4163ad035ccd9a407ec9e519c3b6ba1b633d1e # v1.1.0
        id: current-time-with-day
        with:
          format: YYYYWWd
      - name: <PERSON>ache bazel
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        env:
          cache-name: bazel-cache
        with:
          path: ~/.cache/bazel
          # formattedTime here is like 2021323 - the year concatenated with the week then
          # the day of that week.
          # As this changes every day, we cycle to a new cache once per day, with lookup
          # across the week (and then the year).
          key: ${{ runner.os }}-${{ steps.current-time-with-day.outputs.formattedTime }}
          restore-keys: |
            ${{ runner.os }}-${{ steps.current-time.outputs.formattedTime }}
            ${{ runner.os }}-
      # Cancel current runs if they're still running
      # (saves processing on fast pushes)
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@85880fa0301c86cca9da44039ee3bb12d3bedbfa # 0.12.1
        with:
          access_token: ${{ github.token }}
      # Allow opt-out for some users
      - name: Should I Stay Or Should I Go
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        id: check
        with:
          script: |
            // If you'd like not to run this code on your commits, add your github user id here:
            const NO_AUTOFIX_USERS = ["copybara-service[bot]"];
            const { owner, repo } = context.repo;
            console.log("Actor: " + context.actor);
            if (NO_AUTOFIX_USERS.includes(context.actor)) {
              console.log('Cancelling');
              const run_id = "${{ github.run_id }}";
              await github.rest.actions.cancelWorkflowRun({ owner, repo, run_id });
              return 'go';
            } else {
              return 'stay';
            }
      - name: Wait for cancellation
        run: sleep 60
        if: steps.check.outputs.result == 'go'
      - name: Should build?
        run: test "${{ steps.check.outputs.result }}" = "stay"
      # Setup to run sanity suite
      - name: Install Python Interpreter
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.8
      - name: Install Python Packages
        run: |
          pip install pyyaml mako virtualenv absl-py
          sudo apt-get update
          sudo apt-get install python3-dev
      - name: Check out repository code
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          submodules: True
      - name: Get the upstream code
        run: |
          cd ${{ github.workspace }}
          git remote add upstream https://github.com/grpc/grpc
          git fetch --no-tags --prune --progress --no-recurse-submodules --depth=1 upstream master
      # Run the things!
      - name: clang-tidy fixes
        run: ANDROID_NDK_HOME= ${{ github.workspace }}/tools/distrib/clang_tidy_code.sh --fix --only-changed || true
      - name: Run sanitize
        run: ANDROID_NDK_HOME= ${{ github.workspace }}/tools/distrib/sanitize.sh
      # Report back with a PR if things are broken
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          delete-branch: true
          branch-suffix: short-commit-hash
          commit-message: "Automated change: Fix sanity tests"
          title: Automated fix for ${{ github.ref }}
          body: |
            PanCakes to the rescue!

            We noticed that our 'sanity' test was going to fail, but we think we can fix that automatically, so we put together this PR to do just that!

            If you'd like to opt-out of these PR's, add yourself to NO_AUTOFIX_USERS in .github/workflows/pr-auto-fix.yaml
