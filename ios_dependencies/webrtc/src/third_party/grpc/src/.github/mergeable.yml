mergeable:
  pull_requests:
    label:
      and:
        - must_exclude:
            regex: '^disposition/DO NOT MERGE'
            message: 'Pull request marked not mergeable'
        - must_exclude:
            regex: '^disposition/Needs Internal Changes'
            message: 'Pull request must be cherrypicked. Remove this label when ready to merge.'
        - or:
          - and:
            - must_include:
                regex: '^release notes: yes'
                message: 'Please add the label (release notes: yes)'
            - must_include:
                regex: '^lang\/'
                message: 'Please add a language label (lang/...)'
          - must_include:
              regex: '^release notes: no'
              message: 'Please add the label (release notes: no)'
