---
name: Report a gRPC PHP bug
about: Create a report to help us improve
labels: kind/bug, priority/P2, lang/php
assignees: stanley-cheung

---

<!--
PLEASE DO NOT POST A QUESTION HERE.
This form is for bug reports and feature requests ONLY!

For general questions and troubleshooting, please ask/look for answers at StackOverflow, with "grpc" tag: https://stackoverflow.com/questions/tagged/grpc

For questions that specifically need to be answered by gRPC team members, please ask/look for answers at grpc.io mailing list: https://groups.google.com/forum/#!forum/grpc-io

Issues specific to *grpc-java*, *grpc-go*, *grpc-node*, *grpc-dart*, *grpc-web* should be created in the repository they belong to (e.g. https://github.com/grpc/grpc-LANGUAGE/issues/new)
-->

### What version of gRPC and what language are you using?


### What operating system (Linux, Windows,...) and version?


### What runtime / compiler are you using (e.g. python version or version of gcc)


### What did you do?
Please provide either 1) A unit test for reproducing the bug or 2) Specific steps for us to follow to reproduce the bug. If there’s not enough information to debug the problem, gRPC team may close the issue at their discretion. You’re welcome to re-open the issue once you have a reproduction.

### What did you expect to see?


### What did you see instead?

Make sure you include information that can help us debug (full error message, exception listing, stack trace, logs).

See [TROUBLESHOOTING.md](https://github.com/grpc/grpc/blob/master/TROUBLESHOOTING.md) for how to diagnose problems better.

### Anything else we should know about your project / environment?
