# Copyright 2025 the gRPC authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

module(
    name = "grpc",
    version = "1.74.0-dev",
    compatibility_level = 1,
    repo_name = "com_github_grpc_grpc",
)

# Regular dependencies
# ====================

bazel_dep(name = "abseil-cpp", version = "20250512.1", repo_name = "com_google_absl")
bazel_dep(name = "apple_support", version = "1.17.1", repo_name = "build_bazel_apple_support")
bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "boringssl", version = "0.20241024.0")  # mistmatched 20241211
bazel_dep(name = "c-ares", version = "1.19.1", repo_name = "com_github_cares_cares")
bazel_dep(name = "envoy_api", version = "0.0.0-20250128-4de3c74")
bazel_dep(name = "googleapis", version = "0.0.0-20240819-fe8ba054a", repo_name = "com_google_googleapis")
bazel_dep(name = "googletest", version = "1.16.0", repo_name = "com_google_googletest")
bazel_dep(name = "opencensus-cpp", version = "0.0.0-20230502-50eb5de.bcr.2", repo_name = "io_opencensus_cpp")
bazel_dep(name = "openssl", version = "3.3.1.bcr.1")
bazel_dep(name = "opentelemetry-cpp", version = "1.19.0", repo_name = "io_opentelemetry_cpp")
bazel_dep(name = "platforms", version = "0.0.10")
bazel_dep(name = "protobuf", version = "31.1", repo_name = "com_google_protobuf")
bazel_dep(name = "protoc-gen-validate", version = "1.2.1.bcr.1", repo_name = "com_envoyproxy_protoc_gen_validate")  # Not needed directly
bazel_dep(name = "re2", version = "2024-07-02", repo_name = "com_googlesource_code_re2")  # mistmached 2022-04-01
bazel_dep(name = "rules_apple", version = "3.16.0", repo_name = "build_bazel_rules_apple")
bazel_dep(name = "rules_cc", version = "0.0.17")
bazel_dep(name = "rules_proto", version = "7.0.2")
bazel_dep(name = "xds", version = "0.0.0-20240423-555b57e", repo_name = "com_github_cncf_xds")  # mismatched 20231116
bazel_dep(name = "zlib", version = "1.3.1.bcr.5")

switched_rules = use_extension("@com_google_googleapis//:extensions.bzl", "switched_rules")
switched_rules.use_languages(
    cc = True,
    grpc = True,
    python = True,
)

# Development dependencies
# ========================

bazel_dep(name = "fuzztest", version = "20241028.0", dev_dependency = True, repo_name = "com_google_fuzztest")  # mistmached 2023-05-16
bazel_dep(name = "google_benchmark", version = "1.9.0", dev_dependency = True, repo_name = "com_github_google_benchmark")
bazel_dep(name = "rules_shell", version = "0.4.0", dev_dependency = True)

# Python dependencies
# ===================

bazel_dep(name = "rules_python", version = "0.37.1")

PYTHON_VERSIONS = [
    "3.8",
    "3.9",
    "3.10",
    "3.11",
    "3.12",
    "3.13",
]

python = use_extension("@rules_python//python/extensions:python.bzl", "python")

[
    python.toolchain(
        is_default = python_version == PYTHON_VERSIONS[-1],
        python_version = python_version,
    )
    for python_version in PYTHON_VERSIONS
]

pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")

[
    pip.parse(
        hub_name = "grpc_python_dependencies",
        python_version = python_version,
        requirements_lock = "//:requirements.bazel.lock",
    )
    for python_version in PYTHON_VERSIONS
]

use_repo(pip, "grpc_python_dependencies")

bazel_dep(name = "cython", version = "3.0.11-1")
