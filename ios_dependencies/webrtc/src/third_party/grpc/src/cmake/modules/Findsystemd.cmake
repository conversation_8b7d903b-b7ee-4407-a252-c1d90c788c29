# Copyright 2022 gRPC authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


find_package(systemd QUIET CONFIG)
if(systemd_FOUND)
  message(STATUS "Found systemd via CMake.")
  return()
endif()

if(TARGET systemd)
  message(STATUS "Found systemd via pkg-config already?")
  return()
endif()

find_package(PkgConfig)
pkg_check_modules(SYSTEMD libsystemd>=233)

if(SYSTEMD_FOUND)
  set(systemd_FOUND "${SYSTEMD_FOUND}")
  add_library(systemd INTERFACE IMPORTED)
  message(STATUS "Found systemd via pkg-config.")
endif()
