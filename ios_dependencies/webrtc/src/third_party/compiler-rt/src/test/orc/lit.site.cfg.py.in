@LIT_SITE_CFG_IN_HEADER@

# Tool-specific config options.
config.name_suffix = "@ORC_TEST_CONFIG_SUFFIX@"
config.orc_lit_source_dir = "@ORC_LIT_SOURCE_DIR@"
config.target_cflags = "@ORC_TEST_TARGET_CFLAGS@"
config.target_arch = "@ORC_TEST_TARGET_ARCH@"
config.target_os = "@ORC_TEST_TARGET_OS@"
config.built_with_llvm = ("@COMPILER_RT_STANDALONE_BUILD@" != "TRUE")
config.libunwind_shared = "@LIBUNWIND_ENABLE_SHARED@"
config.libunwind_install_dir = "@LLVM_BINARY_DIR@/@LIBUNWIND_INSTALL_LIBRARY_DIR@"

# Load common config for all compiler-rt lit tests
lit_config.load_config(config, "@COMPILER_RT_BINARY_DIR@/test/lit.common.configured")

# Load tool-specific config that would do the real work.
lit_config.load_config(config, "@CMAKE_CURRENT_SOURCE_DIR@/lit.cfg.py")
