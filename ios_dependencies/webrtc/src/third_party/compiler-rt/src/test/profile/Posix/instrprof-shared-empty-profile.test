"""
This test produces two shared libraries:

1. libt-instr.so is instrumented
2. libt-no-instr.so is built with profile rt linked in (via -u<hook>), but the object file is built
   with instrumentation turned off.

The test verifies concatenating profiles with only headers and no profile data and counters.
"""

RUN: mkdir -p %t.d
RUN: %clang_profgen -o %t.d/libt-instr.so -fPIC -shared %S/../Inputs/instrprof-shared-lib.c
RUN: %clang -c -o %t.d/instrprof-shared-lib-no-instr.o -fPIC  %S/../Inputs/instrprof-shared-lib.c
RUN: %clang_profgen -o %t.d/libt-no-instr.so -fPIC -shared %t.d/instrprof-shared-lib-no-instr.o

# Header + Header
RUN: echo "src:other.c" > %t-file.list
RUN: %clang_profgen -fprofile-list=%t-file.list -o %t-no-instr-no-instr -L%t.d -rpath %t.d -lt-no-instr  %S/../Inputs/instrprof-shared-main.c
RUN: env LLVM_PROFILE_FILE=%t-no-instr-no-instr.profraw %run %t-no-instr-no-instr
RUN: llvm-profdata show %t-no-instr-no-instr.profraw | FileCheck %s --check-prefix=HEADER-HEADER
// HEADER-HEADER: Instrumentation level: Front-end
// HEADER-HEADER-NEXT: Total functions: 0
// HEADER-HEADER-NEXT: Maximum function count: 0
// HEADER-HEADER-NEXT: Maximum internal block count: 0

# Header + Profile
RUN: %clang_profgen -fprofile-list=%t-file.list -o %t-no-instr-instr -L%t.d -rpath %t.d -lt-instr  %S/../Inputs/instrprof-shared-main.c
RUN: env LLVM_PROFILE_FILE=%t-no-instr-instr.profraw %run %t-no-instr-instr
RUN: llvm-profdata show %t-no-instr-instr.profraw | FileCheck %s --check-prefix=HEADER-PROFILE
// HEADER-PROFILE: Instrumentation level: Front-end
// HEADER-PROFILE-NEXT: Total functions: 1
// HEADER-PROFILE-NEXT: Maximum function count: 1000000
// HEADER-PROFILE-NEXT: Maximum internal block count: 360000

# Profile + Header
RUN: %clang_profgen -o %t-instr-no-instr -L%t.d -rpath %t.d -lt-no-instr %S/../Inputs/instrprof-shared-main.c
RUN: env LLVM_PROFILE_FILE=%t-instr-no-instr.profraw %run %t-instr-no-instr
RUN: llvm-profdata show %t-instr-no-instr.profraw | FileCheck %s --check-prefix=PROFILE-HEADER
// PROFILE-HEADER: Instrumentation level: Front-end
// PROFILE-HEADER-NEXT: Total functions: 1
// PROFILE-HEADER-NEXT: Maximum function count: 1
// PROFILE-HEADER-NEXT: Maximum internal block count: 1000000
