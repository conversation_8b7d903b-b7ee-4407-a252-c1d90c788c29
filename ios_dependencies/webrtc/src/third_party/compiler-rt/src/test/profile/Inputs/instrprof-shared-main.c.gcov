// CHECK:        -:    0:Source:{{.*}}Inputs{{[/\\]}}instrprof-shared-main.c
// CHECK-NEXT:        -:    0:Graph:instrprof-shared-main.gcno
// CHECK-NEXT:        -:    0:Data:instrprof-shared-main.gcda
// CHECK-NEXT:        -:    0:Runs:1
// CHECK-NEXT:        -:    1:extern int g1, g2;
// CHECK-NEXT:        -:    2:extern void foo(int n);
// CHECK-NEXT:        -:    3:
// CHECK-NEXT:        1:    4:int main() {
// CHECK-NEXT:        -:    5:  int i, j;
// CHECK-NEXT:     1001:    6:  for (i = 0; i < 1000; i++)
// CHECK-NEXT:  1001000:    7:    for (j = 0; j < 1000; j++)
// CHECK-NEXT:  1001000:    8:      foo(i * j);
// CHECK-NEXT:        -:    9:
// CHECK-NEXT:        1:   10:  if (g2 - g1 == 280001)
// CHECK-NEXT:        1:   11:    return 0;
// CHECK-NEXT:    #####:   12:  return 1;
// CHECK-NEXT:        1:   13:}
