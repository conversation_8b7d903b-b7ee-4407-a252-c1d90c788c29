// CHECK:        -:    0:Source:{{.*}}Inputs{{[/\\]}}instrprof-gcov-switch2.c
// CHECK-NEXT:        -:    0:Graph:instrprof-gcov-switch2.gcno
// CHECK-NEXT:        -:    0:Data:instrprof-gcov-switch2.gcda
// CHECK-NEXT:        -:    0:Runs:1
// CHECK-NEXT:        1:    1:int main(void)
// CHECK-NEXT:        -:    2:{
// CHECK-NEXT:        1:    3:  int i = 22;
// CHECK-NEXT:        -:    4:
// CHECK-NEXT:        1:    5:  switch (i) {
// CHECK-NEXT:        -:    6:    case 7:
// CHECK-NEXT:    #####:    7:      break;
// CHECK-NEXT:        -:    8:
// CHECK-NEXT:        -:    9:    case 22:
// CHECK-NEXT:        1:   10:      i = 7;
// CHECK-NEXT:        -:   11:
// CHECK-NEXT:        -:   12:    case 42:
// CHECK-NEXT:        1:   13:      i = 22;
// CHECK-NEXT:        1:   14:      break;
// CHECK-NEXT:        -:   15:  }
// CHECK-NEXT:        -:   16:
// CHECK-NEXT:        1:   17:  return 0;
// CHECK-NEXT:        -:   18:}
