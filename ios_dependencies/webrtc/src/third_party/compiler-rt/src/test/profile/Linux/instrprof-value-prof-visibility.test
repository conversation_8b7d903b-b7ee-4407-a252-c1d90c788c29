# This tests that lprofMergeValueProfData is not accessed from outside a module
RUN: mkdir -p %t.d
RUN: %clang_profgen -o %t.d/func.shared -fPIC -shared -fuse-ld=gold -mllvm --enable-value-profiling=true %S/../Inputs/instrprof-value-prof-visibility.c -fdata-sections -ffunction-sections -fuse-ld=gold -Wl,--gc-sections
RUN: %clang_profgen -o %t.d/main -fuse-ld=gold -mllvm --enable-value-profiling=true  -DDLOPEN_FUNC_DIR=\"%t.d\" %S/../Inputs/instrprof-value-prof-visibility.c -fdata-sections -ffunction-sections -fuse-ld=gold -Wl,--gc-sections
RUN: env LLVM_PROFILE_FILE=%t.profraw %run %t.d/main

