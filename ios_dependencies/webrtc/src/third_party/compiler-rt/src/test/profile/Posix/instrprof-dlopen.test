RUN: mkdir -p %t.d
RUN: %clang_profgen -o %t.d/func.shared -fPIC -shared %S/../Inputs/instrprof-dlopen-func.c
RUN: %clang_profgen -o %t.d/func2.shared -fPIC -shared %S/../Inputs/instrprof-dlopen-func2.c
RUN: %clang -o %t-local -fPIC -DDLOPEN_FUNC_DIR=\"%t.d\" -DDLOPEN_FLAGS="RTLD_LAZY | RTLD_LOCAL" %S/../Inputs/instrprof-dlopen-main.c
RUN: %clang -o %t-global -fPIC -DDLOPEN_FUNC_DIR=\"%t.d\" -DDLOPEN_FLAGS="RTLD_LAZY | RTLD_GLOBAL" %S/../Inputs/instrprof-dlopen-main.c

RUN: %clang -c -o %t.d/main.o %S/../Inputs/instrprof-dlopen-main.c
RUN: %clang_profgen -o %t-static %S/../Inputs/instrprof-dlopen-func.c %S/../Inputs/instrprof-dlopen-func2.c %t.d/main.o

RUN: env LLVM_PROFILE_FILE=%t-static.profraw %run %t-static
RUN: env LLVM_PROFILE_FILE=%t-local.profraw %run %t-local
RUN: env LLVM_PROFILE_FILE=%t-global.profraw %run %t-global

RUN: llvm-profdata merge -o %t-static.profdata %t-static.profraw
RUN: llvm-profdata merge -o %t-local.profdata %t-local.profraw
RUN: llvm-profdata merge -o %t-global.profdata %t-global.profraw

RUN: %clang_profuse=%t-static.profdata -o %t-func.static.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-func.c
RUN: %clang_profuse=%t-local.profdata -o %t-func.local.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-func.c
RUN: %clang_profuse=%t-global.profdata -o %t-func.global.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-func.c
RUN: diff %t-func.static.ll %t-func.local.ll
RUN: diff %t-func.static.ll %t-func.global.ll

RUN: %clang_profuse=%t-static.profdata -o %t-func2.static.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-func2.c
RUN: %clang_profuse=%t-local.profdata -o %t-func2.local.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-func2.c
RUN: %clang_profuse=%t-global.profdata -o %t-func2.global.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-func2.c
RUN: diff %t-func2.static.ll %t-func2.local.ll
RUN: diff %t-func2.static.ll %t-func2.global.ll

RUN: %clang_profuse=%t-static.profdata -o %t-main.static.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-main.c
RUN: %clang_profuse=%t-local.profdata -o %t-main.local.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-main.c
RUN: %clang_profuse=%t-local.profdata -o %t-main.global.ll -S -emit-llvm %S/../Inputs/instrprof-dlopen-main.c
RUN: diff %t-main.static.ll %t-main.local.ll
RUN: diff %t-main.static.ll %t-main.global.ll
