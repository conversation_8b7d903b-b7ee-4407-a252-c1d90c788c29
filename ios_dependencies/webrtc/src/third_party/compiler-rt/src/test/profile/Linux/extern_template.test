// RUN: %clang -O2  -c -o %t.0.o %S/../Inputs/extern_template.cpp
// RUN: %clang_profgen -O2  -c -o %t.o %S/../Inputs/extern_template.cpp
// RUN: %clang_profgen -O2 -fcoverage-mapping %S/../Inputs/extern_template1.cpp %S/../Inputs/extern_template2.cpp %t.o -o %t
// RUN: env LLVM_PROFILE_FILE=%t.profraw %run %t
// RUN: llvm-profdata show --all-functions %t.profraw | FileCheck %s
// RUN: llvm-profdata merge -o %t.profdata %t.profraw
// RUN: llvm-cov show -instr-profile=%t.profdata %t | FileCheck %S/../Inputs/extern_template.h
// RUN: %clang_profgen -O2 -fcoverage-mapping %S/../Inputs/extern_template1.cpp %S/../Inputs/extern_template2.cpp %t.0.o -o %t.0
// RUN: env LLVM_PROFILE_FILE=%t.0.profraw %run %t.0
// RUN: llvm-profdata show --all-functions %t.0.profraw | FileCheck %s
// RUN: llvm-profdata merge -o %t.0.profdata %t.0.profraw
// RUN: llvm-cov show -instr-profile=%t.0.profdata %t.0 | FileCheck %S/../Inputs/extern_template.h
#define DEF
#include "extern_template.h"
#undef DEF
extern int bar();
extern int foo();
extern Test<int> TO;
int main() {
  foo();
  int R = bar();

  if (R != 10)
    return 1;
  return 0;
}
// No duplicate entries
// CHECK: _ZN4TestIiE4doItEi:
// CHECK-NOT: _ZN4TestIiE4doItEi:
