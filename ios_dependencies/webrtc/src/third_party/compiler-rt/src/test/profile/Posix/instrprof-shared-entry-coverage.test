# When linking an instrumented shared libary, the produced raw profile may have
# multiple headers which must be 8 byte aligned. Check that the counter section
# sizes are 8 byte aligned when using function entry coverage by attempting to
# merge the raw profile.

RUN: mkdir -p %t.d
RUN: %clang_pgogen -mllvm -pgo-function-entry-coverage -o %t.d/libfoo.so -fPIC -shared %S/../Inputs/instrprof-shared-lib.c
RUN: %clang_pgogen -mllvm -pgo-function-entry-coverage -o %t -L%t.d -rpath %t.d -lfoo %S/../Inputs/instrprof-shared-main.c
RUN: env LLVM_PROFILE_FILE=%t.profraw %run %t
RUN: llvm-profdata merge -o %t.profdata %t.profraw
RUN: llvm-profdata show --covered %t.profdata | FileCheck %s

CHECK-DAG: main
CHECK-DAG: foo
