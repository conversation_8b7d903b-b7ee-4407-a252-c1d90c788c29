RUN: mkdir -p %t.d
RUN: cd %t.d

# Test with exceptions disabled.
RUN: %clangxx --coverage -o %t %S/Inputs/instrprof-gcov-exceptions.cpp -fno-exceptions -dumpdir ./
RUN: test -f instrprof-gcov-exceptions.gcno

RUN: rm -f instrprof-gcov-exceptions.gcda
RUN: %run %t
RUN: llvm-cov gcov instrprof-gcov-exceptions.gcda
RUN: FileCheck --match-full-lines --strict-whitespace --input-file instrprof-gcov-exceptions.cpp.gcov %S/Inputs/instrprof-gcov-exceptions.cpp.gcov

# Test with exceptions enabled, the result in terms of line counts should be the same.
RUN: %clangxx --coverage -o %t %S/Inputs/instrprof-gcov-exceptions.cpp -dumpdir ./
RUN: test -f instrprof-gcov-exceptions.gcno

RUN: rm -f instrprof-gcov-exceptions.gcda
RUN: %run %t
RUN: llvm-cov gcov instrprof-gcov-exceptions.gcda
# FIXME: The result should be the same, but they are not on some platforms.
RUNX: FileCheck --match-full-lines --strict-whitespace --input-file instrprof-gcov-exceptions.cpp.gcov %S/Inputs/instrprof-gcov-exceptions.cpp.gcov
