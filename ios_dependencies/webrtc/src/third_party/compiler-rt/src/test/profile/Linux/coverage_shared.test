RUN: mkdir -p %t.d
RUN: %clang_profgen -fdata-sections -ffunction-sections -fcoverage-mapping -c -o %t.d/a.shared.o -fPIC  %S/../Inputs/instrprof-dynamic-a.cpp
RUN: %clang_profgen -fdata-sections -ffunction-sections -fuse-ld=gold -Wl,--gc-sections -fcoverage-mapping -o %t.d/a.shared -fPIC -shared %S/../Inputs/instrprof-dynamic-a.cpp
RUN: %clang_profgen -fdata-sections -ffunction-sections -fuse-ld=gold -Wl,--gc-sections -o %t-shared -fPIC -rpath %t.d %t.d/a.shared %S/../Inputs/instrprof-dynamic-b.cpp %S/../Inputs/instrprof-dynamic-main.cpp

RUN: %clang_profgen -fdata-sections -ffunction-sections -fuse-ld=gold -Wl,--gc-sections -o %t-static %t.d/a.shared.o %S/../Inputs/instrprof-dynamic-b.cpp %S/../Inputs/instrprof-dynamic-main.cpp

RUN: env LLVM_PROFILE_FILE=%t-static.profraw %run %t-static
RUN: env LLVM_PROFILE_FILE=%t-shared.profraw %run %t-shared

RUN: llvm-profdata merge -o %t-static.profdata %t-static.profraw
RUN: llvm-profdata merge -o %t-shared.profdata %t-shared.profraw

RUN: llvm-cov show -instr-profile %t-shared.profdata %t.d/a.shared | FileCheck --check-prefix=COV %S/../Inputs/instrprof-dynamic-a.cpp
RUN: llvm-cov show -instr-profile %t-static.profdata %t-static | FileCheck --check-prefix=COV %S/../Inputs/instrprof-dynamic-a.cpp

