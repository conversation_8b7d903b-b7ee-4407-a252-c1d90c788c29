RUN: mkdir -p %t.d
RUN: %clang_profgen -fprofile-instr-generate %S/../Inputs/instrprof-file_ex.c -o %t
RUN: rm -f %t.d/run.dump
RUN: %run %t %t.d/run.dump
RUN: sort %t.d/run.dump | FileCheck %s

CHECK: Dump from Child 0
CHECK-NEXT: Dump from Child 1
CHECK-NEXT: Dump from Child 2
CHECK-NEXT: Dump from Child 3
CHECK-NEXT: Dump from Child 4
CHECK-NEXT: Dump from Child 5
CHECK-NEXT: Dump from Child 6
CHECK-NEXT: Dump from Child 7 
CHECK-NEXT: Dump from Child 8
CHECK-NEXT: Dump from Child 9
CHECK-NEXT: Dump from parent 10
