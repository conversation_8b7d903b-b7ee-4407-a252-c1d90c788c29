@LIT_SITE_CFG_IN_HEADER@

# Load common config for all compiler-rt unit tests.
lit_config.load_config(config, "@COMPILER_RT_BINARY_DIR@/unittests/lit.common.unit.configured")

# Setup config name.
config.name = 'RealtimeSanitizer-Unit'

# Setup test source and exec root. For unit tests, we define
# it as build directory with ASan unit tests.
# FIXME: De-hardcode this path.
config.test_exec_root = "@COMPILER_RT_BINARY_DIR@/lib/rtsan/tests"
config.test_source_root = config.test_exec_root

if not config.parallelism_group:
  config.parallelism_group = 'shadow-memory'

if config.host_os == 'Darwin':
  # On Darwin, we default to ignore_noninstrumented_modules=1, which also
  # suppresses some races the tests are supposed to find.  See rtsan/lit.cfg.py.
  if 'RTSAN_OPTIONS' in config.environment:
    config.environment['RTSAN_OPTIONS'] += ':ignore_noninstrumented_modules=0'
  else:
    config.environment['RTSAN_OPTIONS'] = 'ignore_noninstrumented_modules=0'
  config.environment['RTSAN_OPTIONS'] += ':ignore_interceptors_accesses=0'
