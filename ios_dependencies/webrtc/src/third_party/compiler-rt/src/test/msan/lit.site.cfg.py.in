@LIT_SITE_CFG_IN_HEADER@

# Tool-specific config options.
config.name_suffix = "-@CONFIG_NAME@"
config.target_cflags = "@MSAN_TEST_TARGET_CFLAGS@"
config.target_arch = "@MSAN_TEST_TARGET_ARCH@"
config.use_lld = @MSAN_TEST_USE_LLD@
config.use_thinlto = @MSAN_TEST_USE_THINLTO@

# Load common config for all compiler-rt lit tests.
lit_config.load_config(config, "@COMPILER_RT_BINARY_DIR@/test/lit.common.configured")

# Load tool-specific config that would do the real work.
lit_config.load_config(config, "@MSAN_LIT_SOURCE_DIR@/lit.cfg.py")
