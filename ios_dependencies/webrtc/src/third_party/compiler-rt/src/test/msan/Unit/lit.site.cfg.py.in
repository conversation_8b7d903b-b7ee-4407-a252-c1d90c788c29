@LIT_SITE_CFG_IN_HEADER@

# Load common config for all compiler-rt unit tests.
lit_config.load_config(config, "@COMPILER_RT_BINARY_DIR@/unittests/lit.common.unit.configured")

# Setup config name.
config.name = 'MemorySanitizer-Unit'

# Setup test source and exec root. For unit tests, we define
# it as build directory with MSan unit tests.
# FIXME: Don't use hardcoded path to MSan unit tests.
config.test_exec_root = "@COMPILER_RT_BINARY_DIR@/lib/msan/tests"
config.test_source_root = config.test_exec_root
