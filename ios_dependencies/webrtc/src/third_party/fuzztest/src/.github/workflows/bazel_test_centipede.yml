# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Bazel Test Centipede

on:
  push:
    branches:
      - main
  pull_request:
  schedule:
    # Triggered nightly at 8:00 AM UTC to reset the Bazel cache.
    - cron: '0 8 * * *'
  workflow_dispatch:

jobs:
  run_tests:
    name: Run Centipede tests
    # TODO(xinhaoyuan): Bump to 24.04 after https://github.com/llvm/llvm-project/issues/102443
    # is fixed.
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    strategy:
      matrix:
        config: ['default', 'noriegeli', 'asan']
    steps:
      - name: Disable core dumping and piping due to slowness
        run: |
          sudo sysctl -w kernel.core_pattern=""
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install dependencies
        run: |
          sudo apt-get update && sudo apt-get install -yq \
            clang llvm libssl-dev
      - name: Restore latest cache
        uses: actions/cache/restore@v4
        with:
          path: "~/.cache/bazel"
          key: bazel-centipede-cache-${{ matrix.config }}
          restore-keys: bazel-centipede-cache-${{ matrix.config }}-
      - name: Run unit tests
        if: matrix.config == 'default'
        run: |
          bazel test --local_test_jobs=1 --test_output=streamed centipede:all
      - name: Run e2e tests
        if: matrix.config == 'default'
        run: |
          bazel test --test_output=errors centipede/testing:instrumentation_test centipede/testing:runner_test
      - name: Run puzzles
        if: matrix.config == 'default'
        run: |
          bazel test --test_output=errors --test_timeout=600 centipede/puzzles:all
      - name: Run puzzles with ASAN
        if: matrix.config == 'asan'
        run: |
          bazel test --test_output=errors --linkopt=-fsanitize=address --copt=-fsanitize=address --test_env=ASAN_OPTIONS=detect_leaks=0 --test_timeout=600 centipede/puzzles:all
      - name: Run unit tests without Riegeli
        if: matrix.config == 'noriegeli'
        run: |
          bazel test --no//fuzztest:use_riegeli --local_test_jobs=1 --test_output=streamed centipede:all
      - name: Run e2e tests without Riegeli
        if: matrix.config == 'noriegeli'
        run: |
          bazel test --no//fuzztest:use_riegeli --test_output=errors centipede/testing:instrumentation_test centipede/testing:runner_test
      - name: Run puzzles without Riegeli
        if: matrix.config == 'noriegeli'
        run: |
          bazel test --no//fuzztest:use_riegeli --test_output=errors --test_timeout=600 centipede/puzzles:all
      - name: Run puzzles without Riegeli with ASAN
        if: matrix.config == 'noriegeli'
        run: |
          bazel test --no//fuzztest:use_riegeli --test_output=errors --linkopt=-fsanitize=address --copt=-fsanitize=address --test_env=ASAN_OPTIONS=detect_leaks=0 --platform_suffix=asan --test_timeout=600 centipede/puzzles:all
      - name: Save new cache based on main
        if: github.ref == 'refs/heads/main'
        uses: actions/cache/save@v4
        with:
          path: "~/.cache/bazel"
          key: bazel-centipede-cache-${{ matrix.config }}-${{ github.run_id }}
  run_tests_mac:
    name: Run Centipede tests (MacOS)
    runs-on: macos-13
    timeout-minutes: 60
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Restore latest cache
        uses: actions/cache/restore@v4
        with:
          path: "~/.cache/bazel"
          key: bazel-centipede-cache-mac
          restore-keys: bazel-centipede-cache-mac-
      - name: Add LLVM symbolizer to path
        run: |
          ln -s $(brew --prefix llvm@15)/bin/llvm-symbolizer /usr/local/bin
      - name: Run unit tests
        if: ${{ !cancelled() }}
        run: |
          # Timed wait is delayed for unknown reasons in MacOS for a few testing
          # environments, thus disabling a few related non-critical tests.
          bazel --output_user_root="${HOME}/.cache/bazel" test --local_test_jobs=1 --test_output=errors --no//fuzztest:use_riegeli -- centipede:all -centipede:periodic_action_test -centipede:rusage_profiler_test && \
          bazel --output_user_root="${HOME}/.cache/bazel" test --local_test_jobs=1 --test_output=errors --no//fuzztest:use_riegeli --test_filter='-*ValidateTimelapseSnapshots' -- centipede:rusage_profiler_test
      - name: Run e2e tests without Riegeli
        if: ${{ !cancelled() }}
        run: |
          bazel --output_user_root="${HOME}/.cache/bazel" test --test_output=errors --no//fuzztest:use_riegeli centipede/testing:instrumentation_test centipede/testing:runner_test centipede/testing:multi_dso_test
      - name: Run puzzles without Riegeli
        if: ${{ !cancelled() }}
        run: |
          bazel --output_user_root="${HOME}/.cache/bazel" test --test_output=errors --no//fuzztest:use_riegeli --test_timeout=600 centipede/puzzles:all
      - name: Run puzzles without Riegeli with ASAN
        if: ${{ !cancelled() }}
        run: |
          bazel --output_user_root="${HOME}/.cache/bazel" test --test_output=errors --no//fuzztest:use_riegeli --linkopt=-fsanitize=address --copt=-fsanitize=address --test_env=ASAN_OPTIONS=detect_leaks=0 --platform_suffix=asan --test_timeout=600 centipede/puzzles:all
      - name: Save new cache based on main
        if: github.ref == 'refs/heads/main'
        uses: actions/cache/save@v4
        with:
          path: "~/.cache/bazel"
          key: bazel-centipede-cache-mac-${{ github.run_id }}
