# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Bazel Test

on:
  push:
    branches:
    - main
  pull_request:
  schedule:
    # Triggered nightly at 8:00 AM UTC to reset the Bazel cache.
    - cron: '0 8 * * *'
  workflow_dispatch:

jobs:
  run_tests:
    name: Run tests
    # TODO(xinhaoyuan): Bump to 24.04 after https://github.com/llvm/llvm-project/issues/102443
    # is fixed.
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    strategy:
      matrix:
        config: ['default', 'fuzztest']
        compilation_mode: ['fastbuild', 'opt', 'dbg']
    steps:
      - name: Disable core dumping and piping due to slowness
        run: |
          sudo sysctl -w kernel.core_pattern=""
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install dependencies
        run: |
          sudo apt-get update && sudo apt-get install -yq \
            clang
      - name: Setup --config=fuzztest for Bazel
        run: |
          EXTRA_CONFIGS=libfuzzer bazel run //bazel:setup_configs > fuzztest.bazelrc
      - name: Restore latest cache
        uses: actions/cache/restore@v4
        with:
          path: "~/.cache/bazel"
          key: bazel-cache-${{ matrix.config }}-${{ matrix.compilation_mode }}
          restore-keys: bazel-cache-${{ matrix.config }}-${{ matrix.compilation_mode }}-
      - name: Run all tests with default --config
        if: matrix.config == 'default'
        run: |
          bazel test --build_tests_only --test_output=errors \
            -c ${{ matrix.compilation_mode }} -- //... -//centipede/...
      - name: Run end-to-end tests with --config=fuzztest
        if: matrix.config == 'fuzztest'
        run: |
          bazel test --build_tests_only --test_output=errors \
            -c ${{ matrix.compilation_mode }} --config=fuzztest //e2e_tests:all
      - name: Run end-to-end tests with --config=fuzztest-experimental
        if: matrix.config == 'fuzztest'
        run: |
          bazel test --build_tests_only --test_output=errors \
            -c ${{ matrix.compilation_mode }} \
            --config=fuzztest-experimental --config=asan \
            --platform_suffix=fuzztest-experimental-asan \
            //e2e_tests:corpus_database_test
      - name: Run end-to-end tests with --config=libfuzzer
        if: matrix.config == 'fuzztest'
        run: |
          bazel test --build_tests_only --test_output=errors \
            -c ${{ matrix.compilation_mode }} --config=libfuzzer \
            --platform_suffix=fuzztest-libfuzzer \
            //e2e_tests:compatibility_mode_test
      - name: Save new cache based on main
        if: github.ref == 'refs/heads/main'
        uses: actions/cache/save@v4
        with:
          path: "~/.cache/bazel"
          key: bazel-cache-${{ matrix.config }}-${{ matrix.compilation_mode }}-${{ github.run_id }}
