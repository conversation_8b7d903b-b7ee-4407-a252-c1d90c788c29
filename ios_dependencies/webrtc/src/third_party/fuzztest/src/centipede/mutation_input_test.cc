// Copyright 2023 The Centipede Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "./centipede/mutation_input.h"

#include <vector>

#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "./common/defs.h"

namespace fuzztest::internal {
namespace {

TEST(MutationInputTest, ConvertsDataToMutationInputRefsAndBack) {
  EXPECT_THAT(
      CopyDataFromMutationInputRefs(GetMutationInputRefsFromDataInputs({})),
      testing::IsEmpty());
  std::vector<ByteArray> data_inputs = {{0}, {1}};
  EXPECT_EQ(CopyDataFromMutationInputRefs(
                GetMutationInputRefsFromDataInputs(data_inputs)),
            data_inputs);
}

}  // namespace
}  // namespace fuzztest::internal
