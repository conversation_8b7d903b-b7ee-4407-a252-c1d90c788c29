// Copyright 2023 The Centipede Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <cstddef>
#include <cstdint>

#include "./centipede/testing/multi_dso_target_lib.h"

// A fuzz target that calls into a different DSO.
extern "C" int LLVMFuzzerTestOneInput(const uint8_t* data, size_t size) {
  if (size >= 2 && data[0] == 'f' && data[1] == 'u') DSO(data, size);
  return 0;
}
