// Copyright 2023 The Centipede Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef THIRD_PARTY_CENTIPEDE_SEED_CORPUS_MAKER_FLAGS_H_
#define THIRD_PARTY_CENTIPEDE_SEED_CORPUS_MAKER_FLAGS_H_

#include <string>

#include "absl/flags/declare.h"

ABSL_DECLARE_FLAG(std::string, config);
ABSL_DECLARE_FLAG(std::string, coverage_binary_path);
ABSL_DECLARE_FLAG(std::string, coverage_binary_hash);
ABSL_DECLARE_FLAG(std::string, override_out_dir);

#endif  // THIRD_PARTY_CENTIPEDE_SEED_CORPUS_MAKER_FLAGS_H_
