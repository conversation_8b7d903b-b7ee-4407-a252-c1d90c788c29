[tox]
envlist = py27,py33,py34,pypy,py37

# Comment to build sdist and install into virtualenv
# This is helpful to test installation but takes extra time
skipsdist = True

[testenv:py27]
deps =
    coverage
    mock
    nose
    -rrequirements.txt
# Some tests expect specific ordering, so we set the hash
# seed for Python 2.x until all tests are updated for Python 3.
# Hash seed affects ordering of certain objects like dict keys
# Set it to a constant so that tests are consistent across
# runs and versions of Python.
setenv =
    PYTHONHASHSEED = 0

[testenv:pypy]
deps =
    coverage
    mock
    nose
    -rrequirements.txt
# See comment above in py27 about hash seed.
setenv =
    PYTHONHASHSEED = 0

[testenv]
deps =
    coverage
    mock
    nose
    -rrequirements.txt
commands =
    python tests/test.py {posargs:default}
