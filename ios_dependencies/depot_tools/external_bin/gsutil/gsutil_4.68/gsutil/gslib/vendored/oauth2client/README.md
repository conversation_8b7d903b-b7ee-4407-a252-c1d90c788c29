[![Build Status](https://travis-ci.org/google/oauth2client.svg?branch=master)](https://travis-ci.org/google/oauth2client)
[![Coverage Status](https://coveralls.io/repos/google/oauth2client/badge.svg?branch=master&service=github)](https://coveralls.io/github/google/oauth2client?branch=master)
[![Documentation Status](https://readthedocs.org/projects/oauth2client/badge/?version=latest)](https://oauth2client.readthedocs.io/)

This is a client library for accessing resources protected by OAuth 2.0.

**Note**: oauth2client is now deprecated. No more features will be added to the
libraries and the core team is turning down support. We recommend you use
[google-auth](https://google-auth.readthedocs.io) and [oauthlib](http://oauthlib.readthedocs.io/). For more details on the deprecation, see [oauth2client deprecation](https://google-auth.readthedocs.io/en/latest/oauth2client-deprecation.html).

Installation
============

To install, simply run the following command in your terminal:

```bash
$ pip install --upgrade oauth2client
```

Contributing
============

Please see the [CONTRIBUTING page][1] for more information. In particular, we
love pull requests -- but please make sure to sign the contributor license
agreement.

Supported Python Versions
=========================

We support Python 2.7 and 3.4+. More information [in the docs][2].

[1]: https://github.com/google/oauth2client/blob/master/CONTRIBUTING.md
[2]: https://oauth2client.readthedocs.io/#supported-python-versions
