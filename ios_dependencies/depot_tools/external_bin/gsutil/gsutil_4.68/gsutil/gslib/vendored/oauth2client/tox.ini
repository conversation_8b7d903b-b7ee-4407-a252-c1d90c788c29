[tox]
envlist = flake8,py27,py34,py35,gae,cover

[testenv]
basedeps = mock>=1.3.0
           pycrypto>=2.6
           cryptography>=1.0
           pyopenssl>=0.14
           webtest
           pytest
           flask
           sqlalchemy
           fasteners
deps = {[testenv]basedeps}
       django
       keyring
       jsonpickle
setenv =
    pypy: with_gmp=no
    DJANGO_SETTINGS_MODULE=tests.contrib.django_util.settings
commands =
    py.test {posargs}

[coverbase]
basepython = python2.7
commands =
    py.test \
      --cov=oauth2client \
      --cov=tests
    py.test \
      --cov=oauth2client \
      --cov=tests \
      --cov-append \
       --gae-sdk={env:GAE_PYTHONPATH:} \
      tests/contrib/appengine
deps = {[testenv]deps}
    coverage
    pytest-cov

[testenv:cover]
basepython = {[coverbase]basepython}
commands =
    {[coverbase]commands}
    coverage report --show-missing --fail-under=100
deps =
    {[coverbase]deps}

[testenv:docs]
basepython = python2.7
deps =
    {[testenv:cover]deps}
    python-gflags
    pyyaml
    sphinx>=1.3b2
    sphinx-rtd-theme
    webapp2
commands = {toxinidir}/scripts/build_docs.sh

[testenv:gae]
basepython = python2.7
deps = {[testenv]basedeps}
commands =
    py.test --gae-sdk={env:GAE_PYTHONPATH:} tests/contrib/appengine

[testenv:system-tests]
basepython =
    python2.7
commands =
    {toxinidir}/scripts/run_system_tests.sh
deps =
    pycrypto>=2.6
    cryptography>=1.0
    pyopenssl>=0.14
passenv = GOOGLE_* OAUTH2CLIENT_* TRAVIS* encrypted_*

[testenv:system-tests3]
basepython =
    python3.4
commands =
    {toxinidir}/scripts/run_system_tests.sh
deps =
    pycrypto>=2.6
    cryptography>=1.0
    pyopenssl>=0.14
passenv = {[testenv:system-tests]passenv}

[testenv:gce-system-tests]
basepython =
    python2.7
commands =
    python {toxinidir}/scripts/run_gce_system_tests.py
deps =
    pycrypto>=2.6
passenv = {[testenv:system-tests]passenv}

[testenv:flake8]
commands = flake8 --import-order-style google {posargs}
deps =
    flake8-putty
    flake8-import-order

[flake8]
exclude = .tox,.git,./*.egg,build,.cache,env,__pycache__
application-import-names = oauth2client, tests
putty-ignore =
  # E402 module level import not at top of file
  # This file has needed configurations defined before import
  docs/conf.py : E402
  # E501 line too long
  # Ignore lines over 80 chars that include "http:" or "https:"
  /http:/ : E501
  /https:/ : E501
  # E722 do not use bare except
  # Existing sloppy usages.
  oauth2client/crypt.py : E722
  oauth2client/contrib/multiprocess_file_storage.py : E722
