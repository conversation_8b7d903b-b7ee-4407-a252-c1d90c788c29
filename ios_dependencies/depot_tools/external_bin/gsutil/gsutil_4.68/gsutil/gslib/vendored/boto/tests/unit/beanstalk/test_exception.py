# Copyright (c) 2014 Amazon.com, Inc. or its affiliates.
# All Rights Reserved
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish, dis-
# tribute, sublicense, and/or sell copies of the Software, and to permit
# persons to whom the Software is furnished to do so, subject to the fol-
# lowing conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABIL-
# ITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
# SHALL THE AUTHOR BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
# IN THE SOFTWARE.

from boto.beanstalk.exception import simple
from tests.compat import unittest


class FakeError(object):
    def __init__(self, code, status, reason, body):
        self.code = code
        self.status = status
        self.reason = reason
        self.body = body


class TestExceptions(unittest.TestCase):
    def test_exception_class_names(self):
        # Create exception from class name
        error = FakeError('TooManyApplications', 400, 'foo', 'bar')
        exception = simple(error)
        self.assertEqual(exception.__class__.__name__, 'TooManyApplications')

        # Create exception from class name + 'Exception' as seen from the
        # live service today
        error = FakeError('TooManyApplicationsException', 400, 'foo', 'bar')
        exception = simple(error)
        self.assertEqual(exception.__class__.__name__, 'TooManyApplications')

        # Make sure message body is present
        self.assertEqual(exception.message, 'bar')
