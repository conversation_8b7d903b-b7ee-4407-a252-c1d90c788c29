# Copyright 2015 Google Inc.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Contains Django URL patterns used for OAuth2 flow."""

from django.conf import urls

from oauth2client.contrib.django_util import views

urlpatterns = [
    urls.url(r'oauth2callback/', views.oauth2_callback, name="callback"),
    urls.url(r'oauth2authorize/', views.oauth2_authorize, name="authorize")
]

urls = (urlpatterns, "google_oauth", "google_oauth")
