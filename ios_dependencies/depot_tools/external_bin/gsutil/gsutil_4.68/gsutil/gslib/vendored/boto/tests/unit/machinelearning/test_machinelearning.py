# Copyright (c) 2015 Amazon.com, Inc. or its affiliates.  All Rights Reserved
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish, dis-
# tribute, sublicense, and/or sell copies of the Software, and to permit
# persons to whom the Software is furnished to do so, subject to the fol-
# lowing conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABIL-
# ITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
# SHALL THE AUTHOR BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
# IN THE SOFTWARE.
#

from boto.compat import json
from boto.machinelearning.layer1 import MachineLearningConnection
from tests.unit import AWSMockServiceTestCase


class TestMachineLearning(AWSMockServiceTestCase):
    connection_class = MachineLearningConnection

    def test_predict(self):
        ml_endpoint = 'mymlmodel.amazonaws.com'
        self.set_http_response(status_code=200, body=b'')
        self.service_connection.predict(
            ml_model_id='foo', record={'Foo': 'bar'},
            predict_endpoint=ml_endpoint)
        self.assertEqual(self.actual_request.host, ml_endpoint)

    def test_predict_with_scheme_in_endpoint(self):
        ml_endpoint = 'mymlmodel.amazonaws.com'
        self.set_http_response(status_code=200, body=b'')
        self.service_connection.predict(
            ml_model_id='foo', record={'Foo': 'bar'},
            predict_endpoint='https://' + ml_endpoint)
        self.assertEqual(self.actual_request.host, ml_endpoint)
